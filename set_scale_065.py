#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.utils.config_manager import ConfigManager

def set_scale_065():
    """设置0.65倍缩放比例"""
    try:
        config_manager = ConfigManager()
        config = config_manager.get_full_config()
        
        # 确保桌面宠物配置存在
        if "DESKTOP_PET" not in config:
            config["DESKTOP_PET"] = {}
        
        # 设置0.65倍缩放比例
        config["DESKTOP_PET"]["size_scale"] = 0.65
        config["DESKTOP_PET"]["enabled"] = True
        
        # 保存配置
        config_manager.save_config(config)
        
        # 计算新的窗口大小
        base_width, base_height = 480, 600
        scale = 0.65
        actual_width = int(base_width * scale)
        actual_height = int(base_height * scale)
        
        # 计算角色显示区域
        char_width = actual_width - 20
        char_height = actual_height - 50
        
        print("✅ 缩放比例已设置为0.65！")
        print(f"窗口大小: {actual_width} x {actual_height} 像素")
        print(f"角色显示区域: {char_width} x {char_height} 像素")
        print(f"滚动文字预留: 50 像素高度")
        
        print("\n特点:")
        print("- 保持宽高比一致，不变形")
        print("- 更紧凑的界面尺寸")
        print("- 滚动文字显示，节省空间")
        print("- 角色显示更自然")
        
        print("\n重新启动应用查看效果:")
        print("python main.py --mode gui")
        print("或测试滚动文字:")
        print("python test_scroll_text.py")
        
    except Exception as e:
        print(f"❌ 设置失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    set_scale_065()
