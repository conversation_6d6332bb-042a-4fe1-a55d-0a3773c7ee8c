#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from src.display.desktop_pet_display import DesktopPetDisplay
from src.utils.config_manager import ConfigManager

def verify_size():
    """验证窗口大小是否正确"""
    try:
        # 检查配置
        config_manager = ConfigManager()
        config = config_manager.get_full_config()
        
        pet_config = config.get("DESKTOP_PET", {})
        size_scale = pet_config.get("size_scale", 1.0)
        
        print(f"📋 配置文件中的缩放比例: {size_scale}")
        
        # 计算预期大小
        base_width, base_height = 480, 600
        expected_width = int(base_width * size_scale)
        expected_height = int(base_height * size_scale)
        
        print(f"📐 预期窗口大小: {expected_width} x {expected_height} 像素")
        
        # 创建应用和窗口
        app = QApplication(sys.argv)
        pet_display = DesktopPetDisplay()
        
        # 获取实际大小
        actual_size = pet_display.size()
        actual_width = actual_size.width()
        actual_height = actual_size.height()
        
        print(f"📏 实际窗口大小: {actual_width} x {actual_height} 像素")
        
        # 比较结果
        if actual_width == expected_width and actual_height == expected_height:
            print("✅ 窗口大小正确！")
        else:
            print("❌ 窗口大小不匹配！")
            print(f"差异: 宽度差 {actual_width - expected_width}, 高度差 {actual_height - expected_height}")
        
        # 显示窗口进行视觉验证
        pet_display.show()
        
        print("\n请视觉检查:")
        print("- 窗口是否显示为312x390像素大小")
        print("- 角色是否保持正确比例")
        print("- 界面是否足够紧凑")
        print("\n按 Ctrl+C 退出")
        
        # 运行应用
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    verify_size()
