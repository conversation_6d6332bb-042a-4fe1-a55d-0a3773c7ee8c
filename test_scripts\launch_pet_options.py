#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import (QApplication, QDialog, QVBoxLayout, QHBoxLayout,
                             QPushButton, QLabel, QButtonGroup, QRadioButton,
                             QSlider, QSpinBox, QMessageBox)
from PyQt5.QtCore import Qt
from src.utils.config_manager import ConfigManager

class PetLaunchDialog(QDialog):
    """桌面宠物启动选项对话框"""
    
    def __init__(self):
        super().__init__()
        self.config_manager = ConfigManager()
        self.setup_ui()
        
    def setup_ui(self):
        """设置UI"""
        self.setWindowTitle("桌面宠物启动选项")
        self.setFixedSize(400, 300)
        
        layout = QVBoxLayout(self)
        
        # 标题
        title = QLabel("选择桌面宠物显示方式")
        title.setStyleSheet("font-size: 16px; font-weight: bold; margin: 10px;")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # 显示方式选择
        self.display_group = QButtonGroup()
        
        # 方案1：紧凑窗口
        self.compact_radio = QRadioButton("紧凑窗口模式")
        self.compact_radio.setToolTip("使用更小的窗口，保留聊天功能")
        self.display_group.addButton(self.compact_radio, 1)
        layout.addWidget(self.compact_radio)
        
        compact_desc = QLabel("• 窗口大小：320x400 基础尺寸")
        compact_desc.setStyleSheet("margin-left: 20px; color: gray;")
        layout.addWidget(compact_desc)
        
        compact_desc2 = QLabel("• 保留完整功能（聊天、设置等）")
        compact_desc2.setStyleSheet("margin-left: 20px; color: gray;")
        layout.addWidget(compact_desc2)
        
        layout.addWidget(QLabel(""))  # 间距
        
        # 方案2：极简模式
        self.minimal_radio = QRadioButton("极简透明模式")
        self.minimal_radio.setToolTip("只显示角色，完全透明背景，无边框")
        self.display_group.addButton(self.minimal_radio, 2)
        layout.addWidget(self.minimal_radio)
        
        minimal_desc = QLabel("• 只显示角色，无窗口边框")
        minimal_desc.setStyleSheet("margin-left: 20px; color: gray;")
        layout.addWidget(minimal_desc)
        
        minimal_desc2 = QLabel("• 完全透明背景，右键菜单控制")
        minimal_desc2.setStyleSheet("margin-left: 20px; color: gray;")
        layout.addWidget(minimal_desc2)
        
        layout.addWidget(QLabel(""))  # 间距
        
        # 缩放比例设置
        scale_layout = QHBoxLayout()
        scale_layout.addWidget(QLabel("缩放比例:"))
        
        self.scale_slider = QSlider(Qt.Horizontal)
        self.scale_slider.setRange(30, 150)  # 0.3 到 1.5
        self.scale_slider.setValue(65)  # 默认0.65
        self.scale_slider.valueChanged.connect(self.update_scale_display)
        scale_layout.addWidget(self.scale_slider)
        
        self.scale_label = QLabel("0.65")
        self.scale_label.setMinimumWidth(40)
        scale_layout.addWidget(self.scale_label)
        
        layout.addLayout(scale_layout)
        
        layout.addWidget(QLabel(""))  # 间距
        
        # 按钮
        button_layout = QHBoxLayout()
        
        self.launch_button = QPushButton("启动")
        self.launch_button.clicked.connect(self.launch_pet)
        button_layout.addWidget(self.launch_button)
        
        self.cancel_button = QPushButton("取消")
        self.cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_button)
        
        layout.addLayout(button_layout)
        
        # 默认选择紧凑模式
        self.compact_radio.setChecked(True)
        
    def update_scale_display(self, value):
        """更新缩放比例显示"""
        scale = value / 100.0
        self.scale_label.setText(f"{scale:.2f}")
        
    def launch_pet(self):
        """启动桌面宠物"""
        try:
            # 获取选择的模式
            selected_id = self.display_group.checkedId()
            scale = self.scale_slider.value() / 100.0
            
            # 更新配置
            config = self.config_manager.get_full_config()
            if "DESKTOP_PET" not in config:
                config["DESKTOP_PET"] = {}
            
            config["DESKTOP_PET"]["size_scale"] = scale
            config["DESKTOP_PET"]["enabled"] = True
            
            self.config_manager.save_config(config)
            
            # 启动对应的宠物
            if selected_id == 1:  # 紧凑窗口模式
                self.launch_compact_mode()
            elif selected_id == 2:  # 极简模式
                self.launch_minimal_mode()
            else:
                QMessageBox.warning(self, "错误", "请选择一种显示模式")
                return
                
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "启动失败", f"启动桌面宠物失败：{e}")
    
    def launch_compact_mode(self):
        """启动紧凑窗口模式"""
        from src.display.desktop_pet_display import DesktopPetDisplay
        
        self.pet_window = DesktopPetDisplay()
        self.pet_window.show()
        
        QMessageBox.information(self, "启动成功", 
                              "紧凑窗口模式已启动！\n"
                              "• 窗口可拖拽移动\n"
                              "• 右键查看更多选项\n"
                              "• 支持语音聊天功能")
    
    def launch_minimal_mode(self):
        """启动极简模式"""
        from src.display.minimal_pet_display import MinimalPetDisplay
        
        self.pet_window = MinimalPetDisplay(self.config_manager)
        self.pet_window.show()
        
        QMessageBox.information(self, "启动成功", 
                              "极简透明模式已启动！\n"
                              "• 左键拖拽移动角色\n"
                              "• 右键查看菜单选项\n"
                              "• 完全透明背景")

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    dialog = PetLaunchDialog()
    if dialog.exec_() == QDialog.Accepted:
        # 保持应用运行
        sys.exit(app.exec_())
    else:
        print("用户取消启动")

if __name__ == "__main__":
    main()
