import ssl
import asyncio
import json
import logging
import websockets

from src.constants.constants import AudioConfig
from src.protocols.protocol import Protocol
from src.utils.config_manager import ConfigManager
from src.utils.logging_config import get_logger

ssl_context = ssl._create_unverified_context()

logger = get_logger(__name__)


class WebsocketProtocol(Protocol):
    def __init__(self):
        super().__init__()
        # 获取配置管理器实例
        self.config = ConfigManager.get_instance()
        self.websocket = None
        self.connected = False
        self.hello_received = None  # 初始化时先设为 None
        self.WEBSOCKET_URL = self.config.get_config(
            "SYSTEM_OPTIONS.NETWORK.WEBSOCKET_URL"
        )
        self.HEADERS = {
            "Authorization": (
                f"Bearer {self.config.get_config('SYSTEM_OPTIONS.NETWORK.WEBSOCKET_ACCESS_TOKEN')}"
            ),
            "Protocol-Version": "1",
            "Device-Id": self.config.get_config(
                "SYSTEM_OPTIONS.DEVICE_ID"
            ),  # 获取设备MAC地址
            "Client-Id": self.config.get_config("SYSTEM_OPTIONS.CLIENT_ID")
        }

    async def connect(self) -> bool:
        """连接到WebSocket服务器，增加重试机制和更好的错误处理"""
        max_retries = 3
        retry_delay = 2.0

        for attempt in range(max_retries):
            try:
                if attempt > 0:
                    logger.info(f"WebSocket连接重试 {attempt + 1}/{max_retries}")
                    await asyncio.sleep(retry_delay * attempt)

                # 在连接时创建 Event，确保在正确的事件循环中
                self.hello_received = asyncio.Event()

                # 判断是否应该使用 SSL
                current_ssl_context = None
                if self.WEBSOCKET_URL.startswith('wss://'):
                    current_ssl_context = ssl_context

                # 建立WebSocket连接 (兼容不同Python版本的写法)
                connect_kwargs = {
                    "ssl": current_ssl_context,
                    "ping_interval": 20,  # 每20秒发送ping
                    "ping_timeout": 10,   # ping超时时间
                    "close_timeout": 10,  # 关闭超时时间
                    "max_size": 2**20,    # 最大消息大小1MB
                    "max_queue": 32,      # 最大队列大小
                }

                try:
                    # 新的写法 (在Python 3.11+版本中)
                    self.websocket = await websockets.connect(
                        uri=self.WEBSOCKET_URL,
                        additional_headers=self.HEADERS,
                        **connect_kwargs
                    )
                except TypeError:
                    # 旧的写法 (在较早的Python版本中)
                    self.websocket = await websockets.connect(
                        self.WEBSOCKET_URL,
                        extra_headers=self.HEADERS,
                        **connect_kwargs
                    )

                # 启动消息处理循环
                asyncio.create_task(self._message_handler())

                # 发送客户端hello消息
                hello_message = {
                    "type": "hello",
                    "version": 1,
                    "transport": "websocket",
                    "audio_params": {
                        "format": "opus",
                        "sample_rate": AudioConfig.INPUT_SAMPLE_RATE,
                        "channels": AudioConfig.CHANNELS,
                        "frame_duration": AudioConfig.FRAME_DURATION,
                    }
                }
                await self.send_text(json.dumps(hello_message))

                # 等待服务器hello响应
                try:
                    await asyncio.wait_for(
                        self.hello_received.wait(),
                        timeout=15.0  # 增加超时时间
                    )
                    self.connected = True
                    logger.info("已连接到WebSocket服务器")
                    return True
                except asyncio.TimeoutError:
                    logger.error("等待服务器hello响应超时")
                    await self._cleanup_connection()
                    if attempt == max_retries - 1:
                        if self.on_network_error:
                            self.on_network_error("等待响应超时")
                        return False
                    continue

            except websockets.exceptions.InvalidStatusCode as e:
                logger.error(f"WebSocket连接状态码错误: {e}")
                if e.status_code == 1005:
                    logger.error("检测到1005错误，可能是服务器配置问题")
                await self._cleanup_connection()
                if attempt == max_retries - 1:
                    if self.on_network_error:
                        self.on_network_error(f"连接状态码错误: {e}")
                    return False
                continue

            except websockets.exceptions.ConnectionClosedError as e:
                logger.error(f"WebSocket连接被关闭: {e}")
                await self._cleanup_connection()
                if attempt == max_retries - 1:
                    if self.on_network_error:
                        self.on_network_error(f"连接被关闭: {e}")
                    return False
                continue

            except Exception as e:
                logger.error(f"WebSocket连接失败: {e}")
                await self._cleanup_connection()
                if attempt == max_retries - 1:
                    if self.on_network_error:
                        self.on_network_error(f"无法连接服务: {str(e)}")
                    return False
                continue

        return False

    async def _cleanup_connection(self):
        """清理连接资源"""
        try:
            if self.websocket:
                await self.websocket.close()
                self.websocket = None
            self.connected = False
        except Exception as e:
            logger.warning(f"清理连接时出错: {e}")

    async def _message_handler(self):
        """处理接收到的WebSocket消息，增加更好的错误处理"""
        try:
            async for message in self.websocket:
                try:
                    if isinstance(message, str):
                        try:
                            data = json.loads(message)
                            msg_type = data.get("type")
                            if msg_type == "hello":
                                # 处理服务器 hello 消息
                                await self._handle_server_hello(data)
                            else:
                                if self.on_incoming_json:
                                    self.on_incoming_json(data)
                        except json.JSONDecodeError as e:
                            logger.error(f"无效的JSON消息: {message[:100]}..., 错误: {e}")
                    elif self.on_incoming_audio:  # 使用 elif 更清晰
                        self.on_incoming_audio(message)
                except Exception as e:
                    logger.error(f"处理单个消息时出错: {e}")
                    continue  # 继续处理下一个消息

        except websockets.ConnectionClosed as e:
            logger.info(f"WebSocket连接已关闭: {e}")
            self.connected = False
            if self.on_audio_channel_closed:
                try:
                    await self.on_audio_channel_closed()
                except Exception as callback_error:
                    logger.error(f"执行连接关闭回调时出错: {callback_error}")

        except websockets.exceptions.ConnectionClosedError as e:
            logger.warning(f"WebSocket连接异常关闭: {e}")
            self.connected = False
            if self.on_network_error:
                try:
                    self.on_network_error(f"连接异常关闭: {str(e)}")
                except Exception as callback_error:
                    logger.error(f"执行网络错误回调时出错: {callback_error}")

        except websockets.exceptions.InvalidStatusCode as e:
            logger.error(f"WebSocket状态码错误: {e}")
            self.connected = False
            if self.on_network_error:
                try:
                    self.on_network_error(f"状态码错误: {str(e)}")
                except Exception as callback_error:
                    logger.error(f"执行网络错误回调时出错: {callback_error}")

        except Exception as e:
            logger.error(f"消息处理器发生未预期错误: {e}")
            self.connected = False
            if self.on_network_error:
                try:
                    self.on_network_error(f"连接错误: {str(e)}")
                except Exception as callback_error:
                    logger.error(f"执行网络错误回调时出错: {callback_error}")
        finally:
            # 确保连接状态正确设置
            self.connected = False
            logger.info("消息处理器已退出")

    async def send_audio(self, data: bytes):
        """发送音频数据"""
        if not self.is_audio_channel_opened():  # 使用已有的 is_connected 方法
            return

        try:
            await self.websocket.send(data)
        except Exception as e:
            if self.on_network_error:
                self.on_network_error(f"发送音频数据失败: {str(e)}")

    async def send_text(self, message: str):
        """发送文本消息"""
        if self.websocket:
            try:
                await self.websocket.send(message)
            except Exception as e:
                await self.close_audio_channel()
                if self.on_network_error:
                    self.on_network_error(f"发送消息失败: {str(e)}")

    def is_audio_channel_opened(self) -> bool:
        """检查音频通道是否打开"""
        return self.websocket is not None and self.connected

    async def open_audio_channel(self) -> bool:
        """建立 WebSocket 连接

        如果尚未连接,则创建新的 WebSocket 连接
        Returns:
            bool: 连接是否成功
        """
        if not self.connected:
            return await self.connect()
        return True

    async def _handle_server_hello(self, data: dict):
        """处理服务器的 hello 消息"""
        try:
            # 验证传输方式
            transport = data.get("transport")
            if not transport or transport != "websocket":
                logger.error(f"不支持的传输方式: {transport}")
                return
            print("服务链接返回初始化配置", data)

            # 设置 hello 接收事件
            self.hello_received.set()

            # 通知音频通道已打开
            if self.on_audio_channel_opened:
                await self.on_audio_channel_opened()

            logger.info("成功处理服务器 hello 消息")

        except Exception as e:
            logger.error(f"处理服务器 hello 消息时出错: {e}")
            if self.on_network_error:
                self.on_network_error(f"处理服务器响应失败: {str(e)}")

    async def close_audio_channel(self):
        """关闭音频通道"""
        if self.websocket:
            try:
                await self.websocket.close()
                self.websocket = None
                self.connected = False
                if self.on_audio_channel_closed:
                    await self.on_audio_channel_closed()
            except Exception as e:
                logger.error(f"关闭WebSocket连接失败: {e}")