import sys
import os
import logging
from typing import Optional, Callable
from PyQt5.QtWidgets import (QWidget, QApplication, QLabel,
                             QMenu, QAction, QInputDialog, QLineEdit, QMessageBox, QScrollArea, QTextEdit)
from PyQt5.QtCore import Qt, QTimer, QPoint, QEvent, QMetaObject, pyqtSlot, QPropertyAnimation, QEasingCurve
import threading
from PyQt5.QtGui import QMovie, QFont, QCursor, QPalette
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.utils.config_manager import ConfigManager

class DesktopPetDisplay(QWidget):
    """桌面宠物显示类"""
    
    # 定义update_ui槽函数
    @pyqtSlot()
    def update_ui(self):
        """更新UI的槽函数"""
        if hasattr(self, '_pending_status_text'):
            text = self._pending_status_text
            duration = self._pending_duration
            self.start_scroll_text(text, duration)

    def update_scroll_text(self):
        """更新滚动文字显示"""
        if not self.current_scroll_text:
            return

        text_len = len(self.current_scroll_text)
        if text_len <= 20:  # 如果文字短，直接显示
            self.scroll_text_label.setText(self.current_scroll_text)
        else:
            # 滚动显示长文字
            visible_text = self.current_scroll_text[self.scroll_position:self.scroll_position + 20]
            if len(visible_text) < 20:  # 到达末尾，从头开始
                visible_text += " | " + self.current_scroll_text[:20 - len(visible_text) - 3]

            self.scroll_text_label.setText(visible_text)
            self.scroll_position = (self.scroll_position + self.scroll_speed) % (text_len + 3)  # +3 for " | "

    def start_scroll_text(self, text, duration):
        """开始滚动文字显示"""
        self.current_scroll_text = text
        self.scroll_position = 0
        self.scroll_text_label.show()

        # 启动滚动定时器
        self.scroll_timer.start(200)  # 每200ms更新一次

        # 设置总显示时间
        QTimer.singleShot(duration, self.stop_scroll_text)

    def stop_scroll_text(self):
        """停止滚动文字显示"""
        self.scroll_timer.stop()
        self.scroll_text_label.hide()
        self.current_scroll_text = ""
    
    def __init__(self):
        super().__init__()
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 初始化BaseDisplay的功能
        self.current_volume = 70
        self.volume_controller = None
        self._init_volume_controller()
        
        # 回调函数
        self.press_callback = None
        self.release_callback = None
        self.status_callback = None
        self.text_callback = None
        self.emotion_callback = None
        self.mode_callback = None
        self.auto_callback = None
        self.abort_callback = None
        self.send_text_callback = None
        
        # 配置管理器
        self.config_manager = ConfigManager()
        
        # 动画相关
        self.current_animation = None
        self.animation_label = None
        self.status_label = None
        
        # 窗口状态
        self.is_dragging = False
        self.drag_position = QPoint()
        
        # 动画状态
        self.current_state = "idle"
        self.animations = {}
        self.emotion_animations = {}
        
        # 初始化UI
        self.setup_ui()
        self.load_animations()
        self.load_position()
        
        # 定时器 - 确保在主线程中创建和启动
        self.update_timer = None
        # 使用singleShot确保在主事件循环中初始化定时器
        QTimer.singleShot(0, self._init_timer)

    @pyqtSlot()
    def _init_timer(self):
        """在主线程中初始化定时器"""
        if QApplication.instance().thread() == threading.current_thread():
            if self.update_timer is None:
                self.update_timer = QTimer(self)
                self.update_timer.timeout.connect(self.process_updates)
                self.update_timer.start(100)  # 100ms更新一次
                self.logger.info("桌面宠物显示初始化完成")
        else:
            # 如果在其他线程中，将操作移到主线程执行
            QMetaObject.invokeMethod(self, "_init_timer", Qt.ConnectionType.QueuedConnection)

    def _init_volume_controller(self):
        """初始化音量控制器"""
        try:
            from src.utils.volume_controller import VolumeController
            if VolumeController.check_dependencies():
                self.volume_controller = VolumeController()
                self.logger.info("音量控制器初始化成功")
                try:
                    self.current_volume = self.volume_controller.get_volume()
                    self.logger.info(f"读取到系统音量: {self.current_volume}%")
                except Exception as e:
                    self.logger.warning(f"获取初始系统音量失败: {e}，将使用默认值 {self.current_volume}%")
            else:
                self.logger.warning("音量控制依赖不满足，将使用默认音量控制")
        except Exception as e:
            self.logger.warning(f"音量控制器初始化失败: {e}，将使用模拟音量控制")

    def setup_ui(self):
        """设置UI布局"""
        # 获取配置中的缩放比例
        try:
            config = self.config_manager.get_full_config()
            pet_config = config.get("DESKTOP_PET", {})
            size_scale = float(pet_config.get("size_scale", 1.0))
        except Exception as e:
            self.logger.warning(f"获取size_scale配置失败: {e}")
            size_scale = 1.0

        # 计算窗口尺寸（确保为整数）
        # 恢复原始窗口大小，只调整角色大小
        base_width, base_height = 480, 600
        scaled_width = int(base_width * size_scale)
        scaled_height = int(base_height * size_scale)

        # 窗口设置
        self.setWindowFlags(
            Qt.FramelessWindowHint |  # 无边框
            Qt.WindowStaysOnTopHint |  # 置顶
            Qt.Tool  # 不在任务栏显示
        )
        self.setAttribute(Qt.WA_TranslucentBackground)  # 透明背景
        self.setFixedSize(scaled_width, scaled_height)  # 使用缩放后的尺寸

        # 使用无布局的绝对定位，避免控件相互遮挡
        # 角色显示标签 - 占据窗口上半部分
        self.animation_label = QLabel(self)
        self.animation_label.setAlignment(Qt.AlignCenter)
        self.animation_label.setStyleSheet("background: transparent;")

        # 设置角色显示区域 - 合理的角色大小
        char_width = int(scaled_width * 0.8)   # 宽度80%，给角色足够空间
        char_height = int(scaled_height * 0.7)  # 高度70%，为文字区域预留空间

        # 角色位置：居中显示在窗口上部
        char_x = (scaled_width - char_width) // 2
        char_y = 15  # 距离顶部15像素

        self.animation_label.setGeometry(char_x, char_y, char_width, char_height)
        self.animation_label.setMaximumSize(char_width, char_height)
        self.animation_label.setMinimumSize(50, 50)

        # 滚动文字显示区域 - 固定在窗口底部
        self.scroll_text_label = QLabel(self)
        self.scroll_text_label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        self.scroll_text_label.setStyleSheet("""
            QLabel {
                background-color: rgba(0, 0, 0, 120);
                color: white;
                font-size: 11px;
                font-weight: bold;
                padding: 4px 8px;
                border-radius: 8px;
                max-height: 25px;
                min-height: 25px;
            }
        """)

        # 文字区域位置：紧贴角色下方，形成气泡效果
        text_height = 30
        # 计算角色底部位置，文字紧贴角色下方
        char_bottom = char_y + char_height
        text_y = char_bottom + 10  # 距离角色底部10像素，形成自然的气泡位置
        text_width = scaled_width - 20  # 左右各留10像素边距

        self.scroll_text_label.setGeometry(10, text_y, text_width, text_height)
        self.scroll_text_label.hide()

        # 滚动相关变量
        self.scroll_timer = QTimer()
        self.scroll_timer.timeout.connect(self.update_scroll_text)
        self.current_scroll_text = ""
        self.scroll_position = 0
        self.scroll_speed = 1  # 每次滚动的字符数
        
        # 先加载动画文件，然后设置默认角色动画
        self.load_animations()
        self.set_character_animation("idle")

    def load_animations(self):
        """加载动画文件"""
        try:
            # 获取基础路径
            if getattr(sys, 'frozen', False):
                # 打包环境
                if hasattr(sys, '_MEIPASS'):
                    base_path = Path(sys._MEIPASS)
                else:
                    base_path = Path(sys.executable).parent
            else:
                # 开发环境
                base_path = Path(__file__).parent.parent.parent
            
            # 构建字符路径和表情路径
            character_path = base_path / "assets" / "characters" / "default"
            emojis_path = base_path / "assets" / "emojis"
            
            # 初始化动画字典
            self.animations = {}
            self.emotion_animations = {}
            
            # 加载基础状态动画
            # 优先使用main.gif，如果不存在则使用对应的状态动画
            main_gif_path = character_path / "main.gif"
            
            if main_gif_path.exists():
                self.logger.info(f"使用主要动画文件: {main_gif_path}")
                # 为所有基础状态使用同一个主要GIF文件
                main_gif_str = str(main_gif_path)
                self.animations = {
                    'idle': main_gif_str,
                    'listening': main_gif_str,
                    'speaking': main_gif_str,
                    'thinking': main_gif_str
                }
                self.logger.info("成功加载主要动画文件 main.gif")
            else:
                # 回退到单独的状态动画文件
                self.logger.info("主要动画文件不存在，加载单独的状态动画文件")
                animation_files = {
                    'idle': character_path / "idle.gif",
                    'listening': character_path / "listening.gif", 
                    'speaking': character_path / "speaking.gif",
                    'thinking': character_path / "thinking.gif"
                }
                
                missing_files = []
                for state, file_path in animation_files.items():
                    if file_path.exists():
                        self.animations[state] = str(file_path)
                        self.logger.debug(f"加载状态动画: {state} -> {file_path}")
                    else:
                        missing_files.append(str(file_path))
                        self.animations[state] = None
                
                if missing_files:
                    self.logger.warning(f"以下状态动画文件缺失: {missing_files}")
            
            # 加载情感动画
            if emojis_path.exists():
                emotion_files = list(emojis_path.glob("*.gif"))
                for emotion_file in emotion_files:
                    emotion_name = emotion_file.stem  # 获取不带扩展名的文件名
                    self.emotion_animations[emotion_name] = str(emotion_file)
                    self.logger.debug(f"加载情感动画: {emotion_name} -> {emotion_file}")
                
                self.logger.info(f"成功加载 {len(self.emotion_animations)} 个情感动画")
            else:
                self.logger.warning(f"情感动画目录不存在: {emojis_path}")
                        
        except Exception as e:
            self.logger.error(f"加载动画时出错: {e}")
            # 设置默认的空动画
            self.animations = {
                'idle': None,
                'listening': None,
                'speaking': None,
                'thinking': None
            }
            self.emotion_animations = {}

    def get_animation_path(self, state):
        """获取动画文件路径
        
        Args:
            state: 动画状态，可以是基础状态(idle, listening, speaking, thinking)
                  或情感状态(happy, sad, neutral等)
        
        Returns:
            str: 动画文件路径，如果未找到返回None
        """
        # 首先检查是否为基础状态动画
        if state in self.animations and self.animations[state]:
            return self.animations[state]
        
        # 然后检查是否为情感动画
        if hasattr(self, 'emotion_animations') and state in self.emotion_animations:
            return self.emotion_animations[state]
        
        # 如果都没找到，返回None
        return None

    def set_character_animation(self, state):
        """设置角色动画状态"""
        try:
            animation_path = self.get_animation_path(state)
            
            if animation_path:
                self.logger.debug(f"设置动画状态: {state} -> {animation_path}")
                
                # 检查文件是否存在
                if os.path.exists(animation_path):
                    # 停止当前动画
                    if hasattr(self, 'current_animation') and self.current_animation:
                        self.current_animation.stop()
                    
                    # 加载并显示新动画
                    self.current_animation = QMovie(animation_path)
                    if not self.current_animation.isValid():
                        self.logger.warning(f"无效的动画文件: {animation_path}")
                        self.set_fallback_display(state)
                        return
                    
                    self.current_animation.setCacheMode(QMovie.CacheAll)
                    
                    # 设置动画到标签，保持宽高比
                    self.animation_label.setMovie(self.current_animation)
                    
                    # 获取动画的原始尺寸并调整显示
                    original_size = self.current_animation.scaledSize()
                    if original_size.isValid():
                        # 使用标签的实际最大尺寸来缩放动画，但角色只占容器的75%
                        label_max_size = self.animation_label.maximumSize()
                        container_width = label_max_size.width()
                        container_height = label_max_size.height()

                        # 角色实际显示尺寸为容器的95%，避免双重缩放
                        available_width = int(container_width * 0.95)
                        available_height = int(container_height * 0.95)

                        # 确保最小尺寸
                        available_width = max(available_width, 100)
                        available_height = max(available_height, 100)

                        # 计算缩放后的尺寸，保持宽高比
                        scaled_size = original_size.scaled(available_width, available_height, Qt.KeepAspectRatio)
                        self.current_animation.setScaledSize(scaled_size)

                        self.logger.debug(f"动画缩放: 原始={original_size.width()}x{original_size.height()}, "
                                        f"容器={container_width}x{container_height}, "
                                        f"角色显示={available_width}x{available_height}, "
                                        f"最终缩放={scaled_size.width()}x{scaled_size.height()}")
                    
                    self.current_animation.start()
                    self.current_state = state
                    
                    self.logger.debug(f"成功加载动画: {animation_path}")
                else:
                    self.logger.warning(f"动画文件不存在: {animation_path}")
                    self.set_fallback_display(state)
            else:
                self.logger.warning(f"未找到状态 '{state}' 的动画路径")
                self.set_fallback_display(state)
                
        except Exception as e:
            self.logger.error(f"设置角色动画时出错: {e}")
            self.set_fallback_display(state)

    def set_fallback_display(self, state):
        """设置备用显示"""
        fallback_text = {
            "idle": "😊",
            "listening": "👂",
            "speaking": "💬",
            "thinking": "🤔"
        }
        
        text = fallback_text.get(state, "😊")
        self.animation_label.setText(text)
        self.animation_label.setStyleSheet("""
            QLabel {
                font-size: 48px;
                color: #4A90E2;
                background: transparent;
            }
        """)

    def load_position(self):
        """加载窗口位置"""
        try:
            config = self.config_manager.get_full_config()
            
            pet_config = config.get("DESKTOP_PET", {})
            position = pet_config.get("position", {})
            
            x = position.get("x", -1)
            y = position.get("y", -1)
            
            if x >= 0 and y >= 0:
                self.move(x, y)
            else:
                # 默认位置：屏幕右下角
                screen = QApplication.desktop().screenGeometry()
                self.move(screen.width() - self.width() - 50, 
                         screen.height() - self.height() - 100)
                         
        except Exception as e:
            self.logger.warning(f"加载位置失败: {e}")
            # 默认位置：屏幕右下角
            screen = QApplication.desktop().screenGeometry()
            self.move(screen.width() - self.width() - 50, 
                     screen.height() - self.height() - 100)

    def save_position(self):
        """保存窗口位置"""
        try:
            config = self.config_manager.get_full_config()
            
            if "DESKTOP_PET" not in config:
                config["DESKTOP_PET"] = {}
            
            config["DESKTOP_PET"]["position"] = {
                "x": self.x(),
                "y": self.y()
            }
            
            self.config_manager.save_config(config)
            
        except Exception as e:
            self.logger.warning(f"保存位置失败: {e}")

    def show_status_bubble(self, text, duration=None):
        """显示滚动文字（替代原来的气泡）"""
        if not text:
            return

        # 根据文本长度自动调整显示时间
        if duration is None:
            # 基础时间5秒，长文本增加时间
            base_duration = 5000
            if len(text) > 20:  # 长文本需要更多时间滚动
                char_duration = len(text) * 150  # 每个字符150毫秒
                duration = min(base_duration + char_duration, 20000)  # 最长20秒
            else:
                duration = base_duration

        # 保存状态信息
        self._pending_status_text = text
        self._pending_duration = duration

        # 确保在主线程中执行UI操作
        if QApplication.instance().thread() == threading.current_thread():
            self.update_ui()
        else:
            # 如果在其他线程中，将操作移到主线程执行
            QMetaObject.invokeMethod(self, "update_ui", Qt.ConnectionType.QueuedConnection)

    @pyqtSlot()
    def process_updates(self):
        """处理更新队列"""
        if not QApplication.instance().thread() == threading.current_thread():
            QMetaObject.invokeMethod(self, "process_updates", Qt.ConnectionType.QueuedConnection)
            return
            
        try:
            # 这里可以添加定期更新的逻辑
            pass
        except Exception as e:
            self.logger.error(f"处理更新失败: {e}")

    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.LeftButton:
            self.is_dragging = True
            self.drag_position = event.globalPos() - self.frameGeometry().topLeft()
            event.accept()
        elif event.button() == Qt.RightButton:
            self.show_context_menu(event.globalPos())

    def mouseMoveEvent(self, event):
        """鼠标移动事件"""
        if event.buttons() == Qt.LeftButton and self.is_dragging:
            self.move(event.globalPos() - self.drag_position)
            event.accept()

    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        if event.button() == Qt.LeftButton:
            self.is_dragging = False
            self.save_position()  # 保存新位置
            event.accept()

    def show_context_menu(self, position):
        """显示右键菜单"""
        menu = QMenu(self)
        
        # 基础设置
        settings_menu = menu.addMenu("基础设置")
        
        # 唤醒词设置
        wake_word_menu = settings_menu.addMenu("唤醒词设置")
        wake_word_enable = wake_word_menu.addAction("启用唤醒词")
        wake_word_enable.setCheckable(True)
        wake_word_enable.setChecked(self.config_manager._config.get('wake_word_enabled', False))
        wake_word_enable.triggered.connect(self.toggle_wake_word)
        
        wake_words_action = wake_word_menu.addAction("设置唤醒词")
        wake_words_action.triggered.connect(self.set_wake_words)
        
        # API设置
        api_menu = settings_menu.addMenu("API设置")
        device_id_action = api_menu.addAction("设置Device ID")
        device_id_action.triggered.connect(self.set_device_id)
        api_url_action = api_menu.addAction("设置API地址")
        api_url_action.triggered.connect(self.set_api_url)
        token_action = api_menu.addAction("设置Access Token")
        token_action.triggered.connect(self.set_access_token)
        
        # Home Assistant设置
        ha_menu = settings_menu.addMenu("Home Assistant设置")
        ha_server_action = ha_menu.addAction("设置HA服务器")
        ha_server_action.triggered.connect(self.set_ha_server)
        ha_port_action = ha_menu.addAction("设置端口")
        ha_port_action.triggered.connect(self.set_ha_port)
        ha_token_action = ha_menu.addAction("设置访问令牌")
        ha_token_action.triggered.connect(self.set_ha_token)

        # 界面设置
        ui_menu = settings_menu.addMenu("界面设置")
        size_action = ui_menu.addAction("调整窗口大小")
        size_action.triggered.connect(self.set_window_size)

        menu.addSeparator()
        
        # 界面切换和退出
        switch_action = menu.addAction("切换到传统界面")
        switch_action.triggered.connect(self.switch_to_gui)
        quit_action = menu.addAction("退出")
        quit_action.triggered.connect(self.quit_application)
        
        menu.exec_(position)

    def toggle_wake_word(self, checked):
        try:
            self.config_manager.update_config('WAKE_WORD_OPTIONS.USE_WAKE_WORD', checked)
            current_config = self.config_manager.get_full_config()
            self.config_manager.save_config(current_config)
            QMessageBox.information(self, '成功', '唤醒词设置已更新')
        except Exception as e:
            self.logger.error(f'更新唤醒词设置失败: {e}')
            QMessageBox.warning(self, '错误', '更新唤醒词设置失败')
    
    def set_wake_words(self):
        current_words = self.config_manager._config.get('wake_words', [])
        text, ok = QInputDialog.getText(self, "设置唤醒词", 
                                    "请输入唤醒词（多个唤醒词用英文逗号分隔）：",
                                    QLineEdit.Normal,
                                    ",".join(current_words))
        if ok and text:
            try:
                wake_words = [word.strip() for word in text.split(',') if word.strip()]
                self.config_manager._config['wake_words'] = wake_words
                self.config_manager.save_config()
                QMessageBox.information(self, "设置成功", "唤醒词已更新")
            except Exception as e:
                QMessageBox.warning(self, "设置失败", f"更新唤醒词时出错：{str(e)}")
    
    def set_device_id(self):
        current_id = self.config_manager._config.get('device_id', '')
        text, ok = QInputDialog.getText(self, "设置Device ID", 
                                    "请输入Device ID：",
                                    QLineEdit.Normal,
                                    current_id)
        if ok and text:
            try:
                self.config_manager._config['device_id'] = text
                self.config_manager.save_config()
                QMessageBox.information(self, "设置成功", "Device ID已更新")
            except Exception as e:
                QMessageBox.warning(self, "设置失败", f"更新Device ID时出错：{str(e)}")
    
    def set_api_url(self):
        current_url = self.config_manager._config.get('websocket_url', '')
        text, ok = QInputDialog.getText(self, "设置API地址", 
                                    "请输入API地址：",
                                    QLineEdit.Normal,
                                    current_url)
        if ok and text:
            try:
                self.config_manager._config['websocket_url'] = text
                self.config_manager.save_config()
                QMessageBox.information(self, "设置成功", "API地址已更新")
            except Exception as e:
                QMessageBox.warning(self, "设置失败", f"更新API地址时出错：{str(e)}")
    
    def set_access_token(self):
        current_token = self.config_manager._config.get('websocket_token', '')
        text, ok = QInputDialog.getText(self, "设置Access Token", 
                                    "请输入Access Token：",
                                    QLineEdit.Password,
                                    current_token)
        if ok and text:
            try:
                self.config_manager._config['websocket_token'] = text
                self.config_manager.save_config()
                QMessageBox.information(self, "设置成功", "Access Token已更新")
            except Exception as e:
                QMessageBox.warning(self, "设置失败", f"更新Access Token时出错：{str(e)}")
    
    def set_ha_server(self):
        current_server = self.config_manager._config.get('ha_server', '')
        text, ok = QInputDialog.getText(self, "设置HA服务器", 
                                    "请输入Home Assistant服务器地址：",
                                    QLineEdit.Normal,
                                    current_server)
        if ok and text:
            try:
                self.config_manager._config['ha_server'] = text
                self.config_manager.save_config()
                QMessageBox.information(self, "设置成功", "HA服务器地址已更新")
            except Exception as e:
                QMessageBox.warning(self, "设置失败", f"更新HA服务器地址时出错：{str(e)}")
    
    def set_ha_port(self):
        current_port = str(self.config_manager._config.get('ha_port', '8123'))
        text, ok = QInputDialog.getText(self, "设置端口", 
                                    "请输入Home Assistant端口号：",
                                    QLineEdit.Normal,
                                    current_port)
        if ok and text:
            try:
                self.config_manager._config['ha_port'] = int(text)
                self.config_manager.save_config()
                QMessageBox.information(self, "设置成功", "端口号已更新")
            except ValueError:
                QMessageBox.warning(self, "设置失败", "请输入有效的端口号")
            except Exception as e:
                QMessageBox.warning(self, "设置失败", f"更新端口号时出错：{str(e)}")
    
    def set_ha_token(self):
        current_token = self.config_manager._config.get('ha_key', '')
        text, ok = QInputDialog.getText(self, "设置访问令牌",
                                    "请输入Home Assistant长期访问令牌：",
                                    QLineEdit.Password,
                                    current_token)
        if ok and text:
            try:
                self.config_manager._config['ha_key'] = text
                self.config_manager.save_config()
                QMessageBox.information(self, "设置成功", "访问令牌已更新")
            except Exception as e:
                QMessageBox.warning(self, "设置失败", f"更新访问令牌时出错：{str(e)}")

    def set_window_size(self):
        """设置窗口大小缩放比例"""
        try:
            config = self.config_manager.get_full_config()
            pet_config = config.get("DESKTOP_PET", {})
            current_scale = pet_config.get("size_scale", 1.0)

            text, ok = QInputDialog.getText(self, "调整窗口大小",
                                        "请输入缩放比例（0.5-3.0，1.0为默认大小）：",
                                        QLineEdit.Normal,
                                        str(current_scale))
            if ok and text:
                try:
                    scale = float(text)
                    if 0.5 <= scale <= 3.0:
                        # 更新配置
                        if "DESKTOP_PET" not in config:
                            config["DESKTOP_PET"] = {}
                        config["DESKTOP_PET"]["size_scale"] = scale
                        self.config_manager.save_config(config)

                        # 重新计算窗口大小
                        base_width, base_height = 480, 600
                        scaled_width = int(base_width * scale)
                        scaled_height = int(base_height * scale)
                        self.setFixedSize(scaled_width, scaled_height)

                        QMessageBox.information(self, "设置成功",
                                              f"窗口大小已调整为 {scaled_width}x{scaled_height} 像素\n"
                                              f"缩放比例: {scale}")
                    else:
                        QMessageBox.warning(self, "设置失败", "缩放比例必须在0.5到3.0之间")
                except ValueError:
                    QMessageBox.warning(self, "设置失败", "请输入有效的数字")
        except Exception as e:
            QMessageBox.warning(self, "设置失败", f"调整窗口大小时出错：{str(e)}")

    def open_settings(self):
        """打开设置"""
        self.logger.info("打开设置")
        # 这里可以添加打开设置窗口的逻辑

    def switch_to_gui(self):
        """切换到传统界面"""
        self.logger.info("切换到传统界面")
        # 这里可以添加切换界面的逻辑

    def quit_application(self):
        """退出应用"""
        self.logger.info("退出应用")
        QApplication.quit()

    # 实现BaseDisplay接口
    def set_callbacks(self,
                     press_callback: Optional[Callable] = None,
                     release_callback: Optional[Callable] = None,
                     status_callback: Optional[Callable] = None,
                     text_callback: Optional[Callable] = None,
                     emotion_callback: Optional[Callable] = None,
                     mode_callback: Optional[Callable] = None,
                     auto_callback: Optional[Callable] = None,
                     abort_callback: Optional[Callable] = None,
                     send_text_callback: Optional[Callable] = None):
        """设置回调函数"""
        self.press_callback = press_callback
        self.release_callback = release_callback
        self.status_callback = status_callback
        self.text_callback = text_callback
        self.emotion_callback = emotion_callback
        self.mode_callback = mode_callback
        self.auto_callback = auto_callback
        self.abort_callback = abort_callback
        self.send_text_callback = send_text_callback

    def update_button_status(self, text: str):
        """更新按钮状态"""
        self.show_status_bubble(f"按钮状态: {text}")

    def update_status(self, status: str):
        """更新状态文本"""
        self.show_status_bubble(status)

    def update_text(self, text: str):
        """更新TTS文本"""
        self.show_status_bubble(f"TTS: {text}")

    def update_emotion(self, emotion: str):
        """更新表情 - 当前已禁用，始终显示main.gif"""
        # 用户要求只显示main.gif，暂时禁用情感动画切换
        self.logger.debug(f"情感更新请求已忽略: {emotion} (当前配置为仅显示main.gif)")
        return

    def get_current_volume(self):
        """获取当前音量"""
        if self.volume_controller:
            try:
                self.current_volume = self.volume_controller.get_volume()
            except Exception as e:
                self.logger.debug(f"获取系统音量失败: {e}")
        return self.current_volume

    def update_volume(self, volume: int):
        """更新系统音量"""
        volume = max(0, min(100, volume))
        self.current_volume = volume
        self.logger.info(f"设置音量: {volume}%")
        
        if self.volume_controller:
            try:
                self.volume_controller.set_volume(volume)
                self.logger.debug(f"系统音量已设置为: {volume}%")
            except Exception as e:
                self.logger.warning(f"设置系统音量失败: {e}")

    def start(self):
        """启动显示"""
        self.show()
        self.logger.info("桌面宠物显示已启动")

    def on_close(self):
        """关闭显示"""
        if self.update_timer:
            self.update_timer.stop()
        
        if self.current_animation:
            self.current_animation.stop()
        
        self.save_position()
        
        self.logger.info("桌面宠物显示已关闭")

    def start_keyboard_listener(self):
        """启动键盘监听"""
        # 桌面宠物模式下可以选择不启用键盘监听
        pass

    def stop_keyboard_listener(self):
        """停止键盘监听"""
        pass