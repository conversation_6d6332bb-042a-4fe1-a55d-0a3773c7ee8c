#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
import subprocess
import time

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from src.display.desktop_pet_display import DesktopPetDisplay
from src.utils.config_manager import ConfigManager

def restart_with_better_size():
    """重启应用，使用更合适的角色大小"""
    try:
        print("🔄 重启桌面宠物，应用新的角色大小...")
        
        # 1. 确保配置正确
        config_manager = ConfigManager()
        config = config_manager.get_full_config()
        
        if "DESKTOP_PET" not in config:
            config["DESKTOP_PET"] = {}
        
        config["DESKTOP_PET"]["enabled"] = True
        config["DESKTOP_PET"]["size_scale"] = 0.65
        
        config_manager.save_config(config)
        print("✅ 配置已确认")
        
        # 2. 计算新的角色尺寸
        base_width, base_height = 480, 600
        window_width = int(base_width * 0.65)  # 312
        window_height = int(base_height * 0.65)  # 390
        
        # 新的角色尺寸（80%宽度，70%高度）
        char_width = int(window_width * 0.8)  # 250
        char_height = int(window_height * 0.7)  # 273
        
        print(f"📐 窗口尺寸: {window_width} x {window_height}")
        print(f"🎭 角色最大尺寸: {char_width} x {char_height}")
        print("📊 角色占窗口比例: 宽度80%, 高度70% (适中大小)")
        
        # 3. 尝试终止现有进程
        try:
            if os.name == 'nt':  # Windows
                subprocess.run(['taskkill', '/f', '/im', 'python.exe'], 
                             capture_output=True, check=False)
                time.sleep(1)
        except:
            pass
        
        # 4. 启动新应用
        print("🚀 启动桌面宠物...")
        
        app = QApplication(sys.argv)
        pet_display = DesktopPetDisplay()
        pet_display.show()
        
        print("✅ 应用已启动！")
        print("\n🎯 现在角色应该:")
        print("- 比之前稍微小一点")
        print("- 不会被聊天框遮挡")
        print("- 不会太小看不清")
        print("- 保持可爱的比例")
        
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ 重启失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    restart_with_better_size()
