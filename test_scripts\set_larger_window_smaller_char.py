#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.utils.config_manager import Config<PERSON>ana<PERSON>

def set_larger_window_smaller_char():
    """设置更大窗口，角色缩小到0.75倍"""
    try:
        print("🔧 设置更大窗口，角色缩小到0.75倍...")
        
        config_manager = ConfigManager()
        config = config_manager.get_full_config()
        
        # 确保桌面宠物配置存在
        if "DESKTOP_PET" not in config:
            config["DESKTOP_PET"] = {}
        
        # 设置更大的窗口尺寸 - 1.3倍
        config["DESKTOP_PET"]["size_scale"] = 1.3  # 增大窗口
        config["DESKTOP_PET"]["enabled"] = True
        
        # 保存配置
        config_manager.save_config(config)
        
        # 计算新的窗口大小
        base_width, base_height = 480, 600
        window_scale = 1.3
        window_width = int(base_width * window_scale)  # 624
        window_height = int(base_height * window_scale)  # 780
        
        # 计算角色容器区域（70% × 60%的窗口空间）
        char_container_width = int(window_width * 0.7)  # 437
        char_container_height = int(window_height * 0.6)  # 468
        
        print("✅ 窗口和角色尺寸已优化！")
        print(f"\n📐 新的尺寸设置:")
        print(f"   窗口大小: {window_width} x {window_height} 像素 (1.3倍)")
        print(f"   角色容器: {char_container_width} x {char_container_height} 像素")
        
        print(f"\n📊 尺寸演进:")
        print(f"   第一版: 312 x 390 (太小)")
        print(f"   第二版: 480 x 600 (改善)")
        print(f"   第三版: {window_width} x {window_height} (最优)")
        
        print(f"\n🎭 角色显示优化:")
        print(f"   容器空间: {char_container_width} x {char_container_height} 像素")
        print(f"   周围留白: 充足的边距，角色不会贴边")
        
        print(f"\n✨ 预期效果:")
        print(f"   🖼️  更大的窗口提供宽敞的显示空间")
        print(f"   🎭 角色在大容器中显示，周围有充足留白")
        print(f"   🎯 角色不会被'框住'，显示更舒适")
        print(f"   📱 整体布局更加美观")
        
        print(f"\n🚀 重新启动应用以查看效果：")
        print(f"   python main.py --mode gui")
        
    except Exception as e:
        print(f"❌ 设置失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    set_larger_window_smaller_char()
