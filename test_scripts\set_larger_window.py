#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.utils.config_manager import ConfigManager

def set_larger_window():
    """设置更大的窗口尺寸"""
    try:
        print("🔧 设置更大的窗口尺寸...")
        
        config_manager = ConfigManager()
        config = config_manager.get_full_config()
        
        # 确保桌面宠物配置存在
        if "DESKTOP_PET" not in config:
            config["DESKTOP_PET"] = {}
        
        # 设置更大的缩放比例 - 从0.65增加到1.0（原始大小）
        config["DESKTOP_PET"]["size_scale"] = 1.0  # 使用原始大小
        config["DESKTOP_PET"]["enabled"] = True
        
        # 保存配置
        config_manager.save_config(config)
        
        # 计算新的窗口大小
        base_width, base_height = 480, 600
        scale = 1.0
        actual_width = int(base_width * scale)
        actual_height = int(base_height * scale)
        
        # 计算角色区域（70% × 60%）
        char_width = int(actual_width * 0.7)  # 336
        char_height = int(actual_height * 0.6)  # 360
        
        print("✅ 窗口大小已更新！")
        print(f"📐 新的窗口大小: {actual_width} x {actual_height} 像素")
        print(f"🎭 角色显示区域: {char_width} x {char_height} 像素")
        print(f"📊 缩放比例: {scale}")
        
        print(f"\n🎯 尺寸对比:")
        print(f"   修改前窗口: 312 x 390 像素 (太小)")
        print(f"   修改后窗口: {actual_width} x {actual_height} 像素 (合适)")
        print(f"   角色区域增大: 218x234 → {char_width}x{char_height} 像素")
        
        print(f"\n✨ 现在角色应该有足够的显示空间了！")
        print("重新启动应用以查看效果：python main.py --mode gui")
        
        # 如果想要更大，可以设置1.2或1.5
        print(f"\n💡 如果还想更大，可以尝试:")
        print(f"   1.2倍: {int(base_width * 1.2)} x {int(base_height * 1.2)} 像素")
        print(f"   1.5倍: {int(base_width * 1.5)} x {int(base_height * 1.5)} 像素")
        
    except Exception as e:
        print(f"❌ 设置失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    set_larger_window()
