#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.utils.config_manager import Config<PERSON>ana<PERSON>

def check_config():
    """检查当前配置"""
    try:
        config_manager = ConfigManager()
        config = config_manager.get_full_config()
        
        print("=== 当前配置状态 ===")
        
        # 检查桌面宠物配置
        pet_config = config.get("DESKTOP_PET", {})
        enabled = pet_config.get("enabled", False)
        size_scale = pet_config.get("size_scale", 1.0)
        
        print(f"桌面宠物模式: {'✅ 已启用' if enabled else '❌ 已禁用'}")
        print(f"窗口缩放比例: {size_scale}")
        
        # 计算实际窗口大小
        base_width, base_height = 480, 600  # 新的基础尺寸
        actual_width = int(base_width * size_scale)
        actual_height = int(base_height * size_scale)
        
        print(f"基础窗口大小: {base_width} x {base_height} 像素")
        print(f"实际窗口大小: {actual_width} x {actual_height} 像素")
        
        # 显示位置信息
        position = pet_config.get("position", {})
        x = position.get("x", -1)
        y = position.get("y", -1)
        print(f"窗口位置: x={x}, y={y}")
        
        print("\n=== 修改说明 ===")
        print("1. 基础窗口大小从 320x450 增加到 480x600 像素")
        print("2. 状态气泡增加了自动换行功能")
        print("3. 字体大小保持为 12px")
        print("4. 添加了窗口大小调整功能")
        print("5. 当前缩放比例设置为 0.75，实际尺寸适中")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查配置失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    check_config()
