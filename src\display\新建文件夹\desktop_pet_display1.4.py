import sys
import os
import logging
from typing import Op<PERSON>, Callable
from PyQt5.QtWidgets import (QWidget, QApplication, QLabel, QVBoxLayout, 
                             QMenu, QAction, QInputDialog, QLineEdit, QMessageBox)
from PyQt5.QtCore import Qt, QTimer, QPoint, QEvent, QMetaObject, pyqtSlot
import threading
from PyQt5.QtGui import QMovie, QFont, QCursor
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.utils.config_manager import ConfigManager

class DesktopPetDisplay(QWidget):
    """桌面宠物显示类"""
    
    # 定义update_ui槽函数
    @pyqtSlot()
    def update_ui(self):
        """更新UI的槽函数"""
        if hasattr(self, '_pending_status_text'):
            text = self._pending_status_text
            duration = self._pending_duration
            self.status_label.setText(text)
            self.status_label.show()
            QTimer.singleShot(duration, self.status_label.hide)
    
    def __init__(self):
        super().__init__()
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 初始化BaseDisplay的功能
        self.current_volume = 70
        self.volume_controller = None
        self._init_volume_controller()
        
        # 回调函数
        self.press_callback = None
        self.release_callback = None
        self.status_callback = None
        self.text_callback = None
        self.emotion_callback = None
        self.mode_callback = None
        self.auto_callback = None
        self.abort_callback = None
        self.send_text_callback = None
        
        # 配置管理器
        self.config_manager = ConfigManager()
        
        # 动画相关
        self.current_animation = None
        self.animation_label = None
        self.status_label = None
        
        # 窗口状态
        self.is_dragging = False
        self.drag_position = QPoint()
        
        # 动画状态
        self.current_state = "idle"
        self.animations = {}
        self.emotion_animations = {}
        
        # 初始化UI
        self.setup_ui()
        self.load_animations()
        self.load_position()
        
        # 定时器 - 确保在主线程中创建和启动
        self.update_timer = None
        # 使用singleShot确保在主事件循环中初始化定时器
        QTimer.singleShot(0, self._init_timer)

    @pyqtSlot()
    def _init_timer(self):
        """在主线程中初始化定时器"""
        if QApplication.instance().thread() == threading.current_thread():
            if self.update_timer is None:
                self.update_timer = QTimer(self)
                self.update_timer.timeout.connect(self.process_updates)
                self.update_timer.start(100)  # 100ms更新一次
                self.logger.info("桌面宠物显示初始化完成")
        else:
            # 如果在其他线程中，将操作移到主线程执行
            QMetaObject.invokeMethod(self, "_init_timer", Qt.ConnectionType.QueuedConnection)

    def _init_volume_controller(self):
        """初始化音量控制器"""
        try:
            from src.utils.volume_controller import VolumeController
            if VolumeController.check_dependencies():
                self.volume_controller = VolumeController()
                self.logger.info("音量控制器初始化成功")
                try:
                    self.current_volume = self.volume_controller.get_volume()
                    self.logger.info(f"读取到系统音量: {self.current_volume}%")
                except Exception as e:
                    self.logger.warning(f"获取初始系统音量失败: {e}，将使用默认值 {self.current_volume}%")
            else:
                self.logger.warning("音量控制依赖不满足，将使用默认音量控制")
        except Exception as e:
            self.logger.warning(f"音量控制器初始化失败: {e}，将使用模拟音量控制")

    def setup_ui(self):
        """设置UI布局"""
        # 窗口设置
        self.setWindowFlags(
            Qt.FramelessWindowHint |  # 无边框
            Qt.WindowStaysOnTopHint |  # 置顶
            Qt.Tool  # 不在任务栏显示
        )
        self.setAttribute(Qt.WA_TranslucentBackground)  # 透明背景
        self.setFixedSize(320, 450)  # 调整窗口尺寸以保持正确比例
        
        # 主布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)  # 添加少量边距
        layout.setSpacing(5)
        
        # 角色显示标签
        self.animation_label = QLabel()
        self.animation_label.setAlignment(Qt.AlignCenter)
        self.animation_label.setStyleSheet("background: transparent;")
        # 设置大小策略和缩放模式以保持宽高比
        self.animation_label.setScaledContents(True)
        self.animation_label.setMinimumSize(1, 1)
        layout.addWidget(self.animation_label, 0, Qt.AlignCenter)
        
        # 状态气泡（初始隐藏）
        self.status_label = QLabel()
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("""
            QLabel {
                background-color: rgba(255, 255, 255, 200);
                border: 2px solid rgba(100, 150, 255, 150);
                border-radius: 15px;
                padding: 8px 12px;
                color: #333;
                font-size: 12px;
                font-weight: bold;
            }
        """)
        self.status_label.hide()
        layout.addWidget(self.status_label)
        
        # 先加载动画文件，然后设置默认角色动画
        self.load_animations()
        self.set_character_animation("idle")

    def load_animations(self):
        """加载动画文件"""
        try:
            # 获取基础路径
            if getattr(sys, 'frozen', False):
                # 打包环境
                if hasattr(sys, '_MEIPASS'):
                    base_path = Path(sys._MEIPASS)
                else:
                    base_path = Path(sys.executable).parent
            else:
                # 开发环境
                base_path = Path(__file__).parent.parent.parent
            
            # 构建字符路径和表情路径
            character_path = base_path / "assets" / "characters" / "default"
            emojis_path = base_path / "assets" / "emojis"
            
            # 初始化动画字典
            self.animations = {}
            self.emotion_animations = {}
            
            # 加载基础状态动画
            # 优先使用main.gif，如果不存在则使用对应的状态动画
            main_gif_path = character_path / "main.gif"
            
            if main_gif_path.exists():
                self.logger.info(f"使用主要动画文件: {main_gif_path}")
                # 为所有基础状态使用同一个主要GIF文件
                main_gif_str = str(main_gif_path)
                self.animations = {
                    'idle': main_gif_str,
                    'listening': main_gif_str,
                    'speaking': main_gif_str,
                    'thinking': main_gif_str
                }
                self.logger.info("成功加载主要动画文件 main.gif")
            else:
                # 回退到单独的状态动画文件
                self.logger.info("主要动画文件不存在，加载单独的状态动画文件")
                animation_files = {
                    'idle': character_path / "idle.gif",
                    'listening': character_path / "listening.gif", 
                    'speaking': character_path / "speaking.gif",
                    'thinking': character_path / "thinking.gif"
                }
                
                missing_files = []
                for state, file_path in animation_files.items():
                    if file_path.exists():
                        self.animations[state] = str(file_path)
                        self.logger.debug(f"加载状态动画: {state} -> {file_path}")
                    else:
                        missing_files.append(str(file_path))
                        self.animations[state] = None
                
                if missing_files:
                    self.logger.warning(f"以下状态动画文件缺失: {missing_files}")
            
            # 加载情感动画
            if emojis_path.exists():
                emotion_files = list(emojis_path.glob("*.gif"))
                for emotion_file in emotion_files:
                    emotion_name = emotion_file.stem  # 获取不带扩展名的文件名
                    self.emotion_animations[emotion_name] = str(emotion_file)
                    self.logger.debug(f"加载情感动画: {emotion_name} -> {emotion_file}")
                
                self.logger.info(f"成功加载 {len(self.emotion_animations)} 个情感动画")
            else:
                self.logger.warning(f"情感动画目录不存在: {emojis_path}")
                        
        except Exception as e:
            self.logger.error(f"加载动画时出错: {e}")
            # 设置默认的空动画
            self.animations = {
                'idle': None,
                'listening': None,
                'speaking': None,
                'thinking': None
            }
            self.emotion_animations = {}

    def get_animation_path(self, state):
        """获取动画文件路径
        
        Args:
            state: 动画状态，可以是基础状态(idle, listening, speaking, thinking)
                  或情感状态(happy, sad, neutral等)
        
        Returns:
            str: 动画文件路径，如果未找到返回None
        """
        # 首先检查是否为基础状态动画
        if state in self.animations and self.animations[state]:
            return self.animations[state]
        
        # 然后检查是否为情感动画
        if hasattr(self, 'emotion_animations') and state in self.emotion_animations:
            return self.emotion_animations[state]
        
        # 如果都没找到，返回None
        return None

    def set_character_animation(self, state):
        """设置角色动画状态"""
        try:
            animation_path = self.get_animation_path(state)
            
            if animation_path:
                self.logger.debug(f"设置动画状态: {state} -> {animation_path}")
                
                # 检查文件是否存在
                if os.path.exists(animation_path):
                    # 停止当前动画
                    if hasattr(self, 'current_animation') and self.current_animation:
                        self.current_animation.stop()
                    
                    # 加载并显示新动画
                    self.current_animation = QMovie(animation_path)
                    if not self.current_animation.isValid():
                        self.logger.warning(f"无效的动画文件: {animation_path}")
                        self.set_fallback_display(state)
                        return
                    
                    self.current_animation.setCacheMode(QMovie.CacheAll)
                    
                    # 设置动画到标签，保持宽高比
                    self.animation_label.setMovie(self.current_animation)
                    
                    # 获取动画的原始尺寸并调整显示
                    original_size = self.current_animation.scaledSize()
                    if original_size.isValid():
                        # 计算合适的显示尺寸，保持宽高比
                        window_size = self.size()
                        # 预留一些边距
                        available_width = window_size.width() - 20
                        available_height = window_size.height() - 20
                        # 计算缩放比例，确保宽度适中
                        scale_ratio = min(available_width / original_size.width(), 
                                        available_height / original_size.height())
                        scaled_size = original_size.scaled(available_width, available_height, Qt.KeepAspectRatio)
                        self.current_animation.setScaledSize(scaled_size)
                    
                    self.current_animation.start()
                    self.current_state = state
                    
                    self.logger.debug(f"成功加载动画: {animation_path}")
                else:
                    self.logger.warning(f"动画文件不存在: {animation_path}")
                    self.set_fallback_display(state)
            else:
                self.logger.warning(f"未找到状态 '{state}' 的动画路径")
                self.set_fallback_display(state)
                
        except Exception as e:
            self.logger.error(f"设置角色动画时出错: {e}")
            self.set_fallback_display(state)

    def set_fallback_display(self, state):
        """设置备用显示"""
        fallback_text = {
            "idle": "😊",
            "listening": "👂",
            "speaking": "💬",
            "thinking": "🤔"
        }
        
        text = fallback_text.get(state, "😊")
        self.animation_label.setText(text)
        self.animation_label.setStyleSheet("""
            QLabel {
                font-size: 48px;
                color: #4A90E2;
                background: transparent;
            }
        """)

    def load_position(self):
        """加载窗口位置"""
        try:
            config = self.config_manager.get_full_config()
            
            pet_config = config.get("DESKTOP_PET", {})
            position = pet_config.get("position", {})
            
            x = position.get("x", -1)
            y = position.get("y", -1)
            
            if x >= 0 and y >= 0:
                self.move(x, y)
            else:
                # 默认位置：屏幕右下角
                screen = QApplication.desktop().screenGeometry()
                self.move(screen.width() - self.width() - 50, 
                         screen.height() - self.height() - 100)
                         
        except Exception as e:
            self.logger.warning(f"加载位置失败: {e}")
            # 默认位置：屏幕右下角
            screen = QApplication.desktop().screenGeometry()
            self.move(screen.width() - self.width() - 50, 
                     screen.height() - self.height() - 100)

    def save_position(self):
        """保存窗口位置"""
        try:
            config = self.config_manager.get_full_config()
            
            if "DESKTOP_PET" not in config:
                config["DESKTOP_PET"] = {}
            
            config["DESKTOP_PET"]["position"] = {
                "x": self.x(),
                "y": self.y()
            }
            
            self.config_manager.save_config(config)
            
        except Exception as e:
            self.logger.warning(f"保存位置失败: {e}")

    def show_status_bubble(self, text, duration=3000):
        """显示状态气泡"""
        if not text:
            return
            
        # 保存状态信息
        self._pending_status_text = text
        self._pending_duration = duration
            
        # 确保在主线程中执行UI操作
        if QApplication.instance().thread() == threading.current_thread():
            self.update_ui()
        else:
            # 如果在其他线程中，将操作移到主线程执行
            QMetaObject.invokeMethod(self, "update_ui", Qt.ConnectionType.QueuedConnection)

    @pyqtSlot()
    def process_updates(self):
        """处理更新队列"""
        if not QApplication.instance().thread() == threading.current_thread():
            QMetaObject.invokeMethod(self, "process_updates", Qt.ConnectionType.QueuedConnection)
            return
            
        try:
            # 这里可以添加定期更新的逻辑
            pass
        except Exception as e:
            self.logger.error(f"处理更新失败: {e}")

    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.LeftButton:
            self.is_dragging = True
            self.drag_position = event.globalPos() - self.frameGeometry().topLeft()
            event.accept()
        elif event.button() == Qt.RightButton:
            self.show_context_menu(event.globalPos())

    def mouseMoveEvent(self, event):
        """鼠标移动事件"""
        if event.buttons() == Qt.LeftButton and self.is_dragging:
            self.move(event.globalPos() - self.drag_position)
            event.accept()

    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        if event.button() == Qt.LeftButton:
            self.is_dragging = False
            self.save_position()  # 保存新位置
            event.accept()

    def show_context_menu(self, position):
        """显示右键菜单"""
        menu = QMenu(self)
        
        # 基础设置
        settings_menu = menu.addMenu("基础设置")
        
        # 唤醒词设置
        wake_word_menu = settings_menu.addMenu("唤醒词设置")
        wake_word_enable = wake_word_menu.addAction("启用唤醒词")
        wake_word_enable.setCheckable(True)
        wake_word_enable.setChecked(self.config_manager._config.get('wake_word_enabled', False))
        wake_word_enable.triggered.connect(self.toggle_wake_word)
        
        wake_words_action = wake_word_menu.addAction("设置唤醒词")
        wake_words_action.triggered.connect(self.set_wake_words)
        
        # API设置
        api_menu = settings_menu.addMenu("API设置")
        device_id_action = api_menu.addAction("设置Device ID")
        device_id_action.triggered.connect(self.set_device_id)
        api_url_action = api_menu.addAction("设置API地址")
        api_url_action.triggered.connect(self.set_api_url)
        token_action = api_menu.addAction("设置Access Token")
        token_action.triggered.connect(self.set_access_token)
        
        # Home Assistant设置
        ha_menu = settings_menu.addMenu("Home Assistant设置")
        ha_server_action = ha_menu.addAction("设置HA服务器")
        ha_server_action.triggered.connect(self.set_ha_server)
        ha_port_action = ha_menu.addAction("设置端口")
        ha_port_action.triggered.connect(self.set_ha_port)
        ha_token_action = ha_menu.addAction("设置访问令牌")
        ha_token_action.triggered.connect(self.set_ha_token)
        
        menu.addSeparator()
        
        # 界面切换和退出
        switch_action = menu.addAction("切换到传统界面")
        switch_action.triggered.connect(self.switch_to_gui)
        quit_action = menu.addAction("退出")
        quit_action.triggered.connect(self.quit_application)
        
        menu.exec_(position)

    def toggle_wake_word(self, checked):
        try:
            self.config_manager.update_config('WAKE_WORD_OPTIONS.USE_WAKE_WORD', checked)
            current_config = self.config_manager.get_full_config()
            self.config_manager.save_config(current_config)
            QMessageBox.information(self, '成功', '唤醒词设置已更新')
        except Exception as e:
            logger.error(f'更新唤醒词设置失败: {e}')
            QMessageBox.warning(self, '错误', '更新唤醒词设置失败')
    
    def set_wake_words(self):
        current_words = self.config_manager._config.get('wake_words', [])
        text, ok = QInputDialog.getText(self, "设置唤醒词", 
                                    "请输入唤醒词（多个唤醒词用英文逗号分隔）：",
                                    QLineEdit.Normal,
                                    ",".join(current_words))
        if ok and text:
            try:
                wake_words = [word.strip() for word in text.split(',') if word.strip()]
                self.config_manager._config['wake_words'] = wake_words
                self.config_manager.save_config()
                QMessageBox.information(self, "设置成功", "唤醒词已更新")
            except Exception as e:
                QMessageBox.warning(self, "设置失败", f"更新唤醒词时出错：{str(e)}")
    
    def set_device_id(self):
        current_id = self.config_manager._config.get('device_id', '')
        text, ok = QInputDialog.getText(self, "设置Device ID", 
                                    "请输入Device ID：",
                                    QLineEdit.Normal,
                                    current_id)
        if ok and text:
            try:
                self.config_manager._config['device_id'] = text
                self.config_manager.save_config()
                QMessageBox.information(self, "设置成功", "Device ID已更新")
            except Exception as e:
                QMessageBox.warning(self, "设置失败", f"更新Device ID时出错：{str(e)}")
    
    def set_api_url(self):
        current_url = self.config_manager._config.get('websocket_url', '')
        text, ok = QInputDialog.getText(self, "设置API地址", 
                                    "请输入API地址：",
                                    QLineEdit.Normal,
                                    current_url)
        if ok and text:
            try:
                self.config_manager._config['websocket_url'] = text
                self.config_manager.save_config()
                QMessageBox.information(self, "设置成功", "API地址已更新")
            except Exception as e:
                QMessageBox.warning(self, "设置失败", f"更新API地址时出错：{str(e)}")
    
    def set_access_token(self):
        current_token = self.config_manager._config.get('websocket_token', '')
        text, ok = QInputDialog.getText(self, "设置Access Token", 
                                    "请输入Access Token：",
                                    QLineEdit.Password,
                                    current_token)
        if ok and text:
            try:
                self.config_manager._config['websocket_token'] = text
                self.config_manager.save_config()
                QMessageBox.information(self, "设置成功", "Access Token已更新")
            except Exception as e:
                QMessageBox.warning(self, "设置失败", f"更新Access Token时出错：{str(e)}")
    
    def set_ha_server(self):
        current_server = self.config_manager._config.get('ha_server', '')
        text, ok = QInputDialog.getText(self, "设置HA服务器", 
                                    "请输入Home Assistant服务器地址：",
                                    QLineEdit.Normal,
                                    current_server)
        if ok and text:
            try:
                self.config_manager._config['ha_server'] = text
                self.config_manager.save_config()
                QMessageBox.information(self, "设置成功", "HA服务器地址已更新")
            except Exception as e:
                QMessageBox.warning(self, "设置失败", f"更新HA服务器地址时出错：{str(e)}")
    
    def set_ha_port(self):
        current_port = str(self.config_manager._config.get('ha_port', '8123'))
        text, ok = QInputDialog.getText(self, "设置端口", 
                                    "请输入Home Assistant端口号：",
                                    QLineEdit.Normal,
                                    current_port)
        if ok and text:
            try:
                self.config_manager._config['ha_port'] = int(text)
                self.config_manager.save_config()
                QMessageBox.information(self, "设置成功", "端口号已更新")
            except ValueError:
                QMessageBox.warning(self, "设置失败", "请输入有效的端口号")
            except Exception as e:
                QMessageBox.warning(self, "设置失败", f"更新端口号时出错：{str(e)}")
    
    def set_ha_token(self):
        current_token = self.config_manager._config.get('ha_key', '')
        text, ok = QInputDialog.getText(self, "设置访问令牌", 
                                    "请输入Home Assistant长期访问令牌：",
                                    QLineEdit.Password,
                                    current_token)
        if ok and text:
            try:
                self.config_manager._config['ha_key'] = text
                self.config_manager.save_config()
                QMessageBox.information(self, "设置成功", "访问令牌已更新")
            except Exception as e:
                QMessageBox.warning(self, "设置失败", f"更新访问令牌时出错：{str(e)}")

    def open_settings(self):
        """打开设置"""
        self.logger.info("打开设置")
        # 这里可以添加打开设置窗口的逻辑

    def switch_to_gui(self):
        """切换到传统界面"""
        self.logger.info("切换到传统界面")
        # 这里可以添加切换界面的逻辑

    def quit_application(self):
        """退出应用"""
        self.logger.info("退出应用")
        QApplication.quit()

    # 实现BaseDisplay接口
    def set_callbacks(self,
                     press_callback: Optional[Callable] = None,
                     release_callback: Optional[Callable] = None,
                     status_callback: Optional[Callable] = None,
                     text_callback: Optional[Callable] = None,
                     emotion_callback: Optional[Callable] = None,
                     mode_callback: Optional[Callable] = None,
                     auto_callback: Optional[Callable] = None,
                     abort_callback: Optional[Callable] = None,
                     send_text_callback: Optional[Callable] = None):
        """设置回调函数"""
        self.press_callback = press_callback
        self.release_callback = release_callback
        self.status_callback = status_callback
        self.text_callback = text_callback
        self.emotion_callback = emotion_callback
        self.mode_callback = mode_callback
        self.auto_callback = auto_callback
        self.abort_callback = abort_callback
        self.send_text_callback = send_text_callback

    def update_button_status(self, text: str):
        """更新按钮状态"""
        self.show_status_bubble(f"按钮状态: {text}")

    def update_status(self, status: str):
        """更新状态文本"""
        self.show_status_bubble(status)

    def update_text(self, text: str):
        """更新TTS文本"""
        self.show_status_bubble(f"TTS: {text}")

    def update_emotion(self, emotion: str):
        """更新表情 - 当前已禁用，始终显示main.gif"""
        # 用户要求只显示main.gif，暂时禁用情感动画切换
        self.logger.debug(f"情感更新请求已忽略: {emotion} (当前配置为仅显示main.gif)")
        return

    def get_current_volume(self):
        """获取当前音量"""
        if self.volume_controller:
            try:
                self.current_volume = self.volume_controller.get_volume()
            except Exception as e:
                self.logger.debug(f"获取系统音量失败: {e}")
        return self.current_volume

    def update_volume(self, volume: int):
        """更新系统音量"""
        volume = max(0, min(100, volume))
        self.current_volume = volume
        self.logger.info(f"设置音量: {volume}%")
        
        if self.volume_controller:
            try:
                self.volume_controller.set_volume(volume)
                self.logger.debug(f"系统音量已设置为: {volume}%")
            except Exception as e:
                self.logger.warning(f"设置系统音量失败: {e}")

    def start(self):
        """启动显示"""
        self.show()
        self.logger.info("桌面宠物显示已启动")

    def on_close(self):
        """关闭显示"""
        if self.update_timer:
            self.update_timer.stop()
        
        if self.current_animation:
            self.current_animation.stop()
        
        self.save_position()
        
        self.logger.info("桌面宠物显示已关闭")

    def start_keyboard_listener(self):
        """启动键盘监听"""
        # 桌面宠物模式下可以选择不启用键盘监听
        pass

    def stop_keyboard_listener(self):
        """停止键盘监听"""
        pass