#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from src.display.desktop_pet_display import DesktopPetDisplay
from src.utils.config_manager import ConfigMana<PERSON>

def test_size_fix():
    """测试尺寸修复效果"""
    try:
        # 确保桌面宠物模式已启用，缩放比例为0.75
        config_manager = ConfigManager()
        config = config_manager.get_full_config()
        
        if "DESKTOP_PET" not in config:
            config["DESKTOP_PET"] = {}
        
        config["DESKTOP_PET"]["enabled"] = True
        config["DESKTOP_PET"]["size_scale"] = 0.75
        config_manager.save_config(config)
        
        # 创建应用
        app = QApplication(sys.argv)
        
        # 创建桌面宠物显示
        pet_display = DesktopPetDisplay()
        
        # 显示窗口信息
        size = pet_display.size()
        print(f"窗口大小: {size.width()} x {size.height()} 像素")
        
        # 计算角色显示区域
        available_width = size.width() - 40
        available_height = size.height() - 120
        print(f"角色显示区域: {available_width} x {available_height} 像素")
        print(f"气泡预留高度: 120 像素")
        
        print("\n修复内容:")
        print("1. ✅ 为气泡预留了120像素高度")
        print("2. ✅ 设置了动画标签的最大尺寸")
        print("3. ✅ 保持宽高比，防止变形")
        print("4. ✅ 角色应该完整显示在窗口内")
        
        # 显示窗口
        pet_display.show()
        
        # 测试气泡显示
        def test_bubble():
            pet_display.show_status_bubble("测试气泡显示，角色应该完整可见，气泡不应该被遮挡。")
            
        # 2秒后显示气泡
        QTimer.singleShot(2000, test_bubble)
        
        print("\n请检查:")
        print("- 角色是否完整显示在窗口内（不被截断）")
        print("- 角色比例是否正常（不变形）")
        print("- 气泡是否正常显示（不被遮挡）")
        print("- 整体布局是否协调")
        print("\n2秒后将显示测试气泡")
        print("按 Ctrl+C 退出测试")
        
        # 运行应用
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_size_fix()
