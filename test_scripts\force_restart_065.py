#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
import subprocess
import time

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.utils.config_manager import Config<PERSON>ana<PERSON>

def force_restart_065():
    """强制重新启动并应用0.65缩放"""
    try:
        print("🔄 强制重新启动桌面宠物...")
        
        # 1. 确保配置正确
        config_manager = ConfigManager()
        config = config_manager.get_full_config()
        
        if "DESKTOP_PET" not in config:
            config["DESKTOP_PET"] = {}
        
        config["DESKTOP_PET"]["enabled"] = True
        config["DESKTOP_PET"]["size_scale"] = 0.65
        
        # 强制保存配置
        success = config_manager.save_config(config)
        if success:
            print("✅ 配置已强制更新为0.65缩放")
        else:
            print("❌ 配置保存失败")
            return
        
        # 2. 验证配置
        new_config = config_manager.get_full_config()
        actual_scale = new_config.get("DESKTOP_PET", {}).get("size_scale", 1.0)
        print(f"📋 验证配置: size_scale = {actual_scale}")
        
        if actual_scale != 0.65:
            print("❌ 配置验证失败，缩放比例不正确")
            return
        
        # 3. 计算预期窗口大小
        base_width, base_height = 480, 600
        expected_width = int(base_width * 0.65)
        expected_height = int(base_height * 0.65)
        
        print(f"📐 预期窗口大小: {expected_width} x {expected_height} 像素")
        
        # 4. 尝试终止现有进程（如果有的话）
        try:
            if os.name == 'nt':  # Windows
                subprocess.run(['taskkill', '/f', '/im', 'python.exe'], 
                             capture_output=True, check=False)
                time.sleep(1)
        except:
            pass
        
        print("🚀 启动桌面宠物应用...")
        print("请检查新启动的窗口是否为312x390像素大小")
        print("如果仍然不正确，请手动重新启动应用：")
        print("python main.py --mode gui")
        
        # 5. 启动应用
        try:
            subprocess.Popen([sys.executable, "main.py", "--mode", "gui"], 
                           cwd=os.getcwd())
            print("✅ 应用已启动")
        except Exception as e:
            print(f"❌ 启动失败: {e}")
            print("请手动运行: python main.py --mode gui")
        
    except Exception as e:
        print(f"❌ 强制重启失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    force_restart_065()
