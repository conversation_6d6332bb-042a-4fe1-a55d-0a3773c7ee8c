#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.utils.config_manager import ConfigManager

def set_larger_size():
    """设置更大的窗口尺寸"""
    try:
        config_manager = ConfigManager()
        config = config_manager.get_full_config()
        
        # 确保桌面宠物配置存在
        if "DESKTOP_PET" not in config:
            config["DESKTOP_PET"] = {}
        
        # 设置更大的缩放比例
        config["DESKTOP_PET"]["size_scale"] = 1.2  # 比默认大20%
        config["DESKTOP_PET"]["enabled"] = True
        
        # 保存配置
        config_manager.save_config(config)
        
        # 计算新的窗口大小
        base_width, base_height = 480, 600
        scale = 1.2
        actual_width = int(base_width * scale)
        actual_height = int(base_height * scale)
        
        print("✅ 窗口大小已更新！")
        print(f"新的窗口大小: {actual_width} x {actual_height} 像素")
        print(f"缩放比例: {scale}")
        print("\n现在聊天文字应该有更多空间显示了！")
        print("重新启动应用以查看效果：python main.py --mode gui")
        
    except Exception as e:
        print(f"❌ 设置失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    set_larger_size()
