#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from src.display.desktop_pet_display import DesktopPetDisplay
from src.utils.config_manager import Config<PERSON><PERSON><PERSON>

def test_character_size():
    """测试角色大小调整"""
    try:
        print("🧪 测试角色大小调整...")
        
        # 确保配置正确
        config_manager = ConfigManager()
        config = config_manager.get_full_config()
        
        if "DESKTOP_PET" not in config:
            config["DESKTOP_PET"] = {}
        
        config["DESKTOP_PET"]["enabled"] = True
        config["DESKTOP_PET"]["size_scale"] = 0.65
        
        config_manager.save_config(config)
        print("✅ 配置已设置为0.65缩放")
        
        # 计算预期尺寸
        base_width, base_height = 480, 600
        window_width = int(base_width * 0.65)  # 312
        window_height = int(base_height * 0.65)  # 390
        
        # 角色预期尺寸（窗口的60%宽度，50%高度）
        char_width = int(window_width * 0.6)  # 187
        char_height = int(window_height * 0.5)  # 195
        
        print(f"📐 窗口尺寸: {window_width} x {window_height}")
        print(f"🎭 角色最大尺寸: {char_width} x {char_height}")
        print(f"📊 角色占窗口比例: 宽度60%, 高度50%")
        
        # 创建应用
        app = QApplication(sys.argv)
        pet_display = DesktopPetDisplay()
        
        # 获取实际尺寸
        window_size = pet_display.size()
        char_max_size = pet_display.animation_label.maximumSize()
        
        print(f"\n📏 实际窗口尺寸: {window_size.width()} x {window_size.height()}")
        print(f"🎭 实际角色最大尺寸: {char_max_size.width()} x {char_max_size.height()}")
        
        # 验证
        if (window_size.width() == window_width and 
            window_size.height() == window_height and
            char_max_size.width() == char_width and
            char_max_size.height() == char_height):
            print("✅ 尺寸设置正确！")
        else:
            print("❌ 尺寸设置有误")
            
        # 显示窗口
        pet_display.show()
        
        print("\n🔍 请检查:")
        print("- 角色是否比之前更小")
        print("- 角色是否不会被聊天框遮挡")
        print("- 窗口底部是否有足够空间显示文字")
        print("\n按 Ctrl+C 退出测试")
        
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_character_size()
