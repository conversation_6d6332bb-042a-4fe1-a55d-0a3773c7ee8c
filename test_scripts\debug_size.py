#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.utils.config_manager import ConfigManager

def debug_size():
    """调试窗口大小设置"""
    try:
        print("🔍 调试窗口大小设置过程...")
        
        # 1. 检查配置管理器
        config_manager = ConfigManager()
        print(f"📁 配置文件路径: {config_manager.CONFIG_FILE}")
        
        # 2. 获取完整配置
        config = config_manager.get_full_config()
        print("📋 完整配置:")
        
        # 3. 检查DESKTOP_PET配置
        pet_config = config.get("DESKTOP_PET", {})
        print(f"🐾 DESKTOP_PET配置: {pet_config}")
        
        # 4. 获取size_scale
        size_scale = pet_config.get("size_scale", 1.0)
        print(f"📏 size_scale值: {size_scale} (类型: {type(size_scale)})")
        
        # 5. 计算窗口大小
        base_width, base_height = 480, 600
        print(f"📐 基础尺寸: {base_width} x {base_height}")
        
        scaled_width = int(base_width * size_scale)
        scaled_height = int(base_height * size_scale)
        print(f"🎯 计算后尺寸: {scaled_width} x {scaled_height}")
        
        # 6. 验证计算
        expected_width = int(480 * 0.65)  # 312
        expected_height = int(600 * 0.65)  # 390
        print(f"✅ 预期尺寸: {expected_width} x {expected_height}")
        
        if scaled_width == expected_width and scaled_height == expected_height:
            print("✅ 计算正确！")
        else:
            print("❌ 计算错误！")
            
        # 7. 检查是否有其他影响因素
        position = pet_config.get("position", {})
        print(f"📍 窗口位置: {position}")
        
        # 8. 强制更新配置
        print("\n🔄 强制更新配置...")
        config["DESKTOP_PET"]["size_scale"] = 0.65
        success = config_manager.save_config(config)
        print(f"💾 保存结果: {'成功' if success else '失败'}")
        
        # 9. 重新读取验证
        new_config = config_manager.get_full_config()
        new_scale = new_config.get("DESKTOP_PET", {}).get("size_scale", 1.0)
        print(f"🔄 重新读取的size_scale: {new_scale}")
        
        print("\n📝 总结:")
        print(f"- 配置文件中的缩放比例: {new_scale}")
        print(f"- 应该的窗口大小: {int(480 * new_scale)} x {int(600 * new_scale)}")
        print("- 如果应用仍然显示错误大小，请完全关闭应用后重新启动")
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_size()
