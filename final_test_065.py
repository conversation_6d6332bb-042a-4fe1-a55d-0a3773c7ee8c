#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from src.display.desktop_pet_display import DesktopPetDisplay
from src.utils.config_manager import ConfigMana<PERSON>

def final_test_065():
    """最终测试0.65缩放效果"""
    try:
        # 确保配置正确
        config_manager = ConfigManager()
        config = config_manager.get_full_config()
        
        if "DESKTOP_PET" not in config:
            config["DESKTOP_PET"] = {}
        
        config["DESKTOP_PET"]["enabled"] = True
        config["DESKTOP_PET"]["size_scale"] = 0.65
        config_manager.save_config(config)
        
        # 创建应用
        app = QApplication(sys.argv)
        
        # 创建桌面宠物显示
        pet_display = DesktopPetDisplay()
        
        # 显示窗口信息
        size = pet_display.size()
        print(f"✅ 最终窗口大小: {size.width()} x {size.height()} 像素")
        print(f"缩放比例: 0.65")
        
        # 计算显示区域
        char_width = size.width() - 20
        char_height = size.height() - 50
        print(f"角色显示区域: {char_width} x {char_height} 像素")
        
        print("\n✅ 最终优化效果:")
        print("1. 紧凑的界面尺寸（312×390像素）")
        print("2. 保持宽高比，角色不变形")
        print("3. 滚动文字显示，节省空间")
        print("4. 角色显示更自然，不被框住")
        print("5. 半透明文字条，不遮挡角色")
        
        # 显示窗口
        pet_display.show()
        
        # 测试滚动文字
        def test_final():
            pet_display.show_status_bubble("最终测试：这是一段较长的文字，用来验证0.65缩放下的滚动文字效果。界面应该更紧凑，角色保持正确比例。")
            print("显示最终测试文字...")
        
        # 3秒后显示测试文字
        QTimer.singleShot(3000, test_final)
        
        print("\n请检查:")
        print("- 窗口大小是否合适（不太大不太小）")
        print("- 角色比例是否正常（不变形）")
        print("- 滚动文字是否正常工作")
        print("- 整体界面是否协调美观")
        print("\n3秒后将显示测试文字")
        print("按 Ctrl+C 退出测试")
        
        # 运行应用
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    final_test_065()
