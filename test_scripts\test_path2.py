import os
import sys

# 模拟desktop_pet_display.py中的路径计算
print("模拟desktop_pet_display.py中的路径计算:")

# 假设当前文件是src/display/desktop_pet_display.py
simulated_file = os.path.join(os.getcwd(), "src", "display", "desktop_pet_display.py")
print(f"模拟文件路径: {simulated_file}")

# 计算项目根目录
current_file = os.path.abspath(simulated_file)
base_path = os.path.dirname(os.path.dirname(os.path.dirname(current_file)))
print(f"计算的项目根目录: {base_path}")

character_path = os.path.join(base_path, "assets", "characters", "default")
print(f"动画文件搜索路径: {character_path}")

animation_states = ["main", "idle", "listening", "speaking", "thinking"]
print()

for state in animation_states:
    animation_file = os.path.join(character_path, f"{state}.gif")
    exists = os.path.exists(animation_file)
    print(f"{state}.gif: {animation_file} - {'存在' if exists else '不存在'}")
    
print()
print("实际文件检查:")
actual_path = os.path.join(os.getcwd(), "assets", "characters", "default", "main.gif")
print(f"实际main.gif路径: {actual_path}")
print(f"实际main.gif存在: {os.path.exists(actual_path)}") 