#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from src.display.desktop_pet_display import DesktopPetDisplay
from src.utils.config_manager import Config<PERSON><PERSON><PERSON>

def test_fixed_size():
    """测试修复后的角色大小"""
    try:
        print("🔧 测试修复后的角色大小...")
        
        # 确保配置正确
        config_manager = ConfigManager()
        config = config_manager.get_full_config()
        
        if "DESKTOP_PET" not in config:
            config["DESKTOP_PET"] = {}
        
        config["DESKTOP_PET"]["enabled"] = True
        config["DESKTOP_PET"]["size_scale"] = 0.65
        
        config_manager.save_config(config)
        print("✅ 配置已设置")
        
        # 计算预期尺寸
        base_width, base_height = 480, 600
        window_width = int(base_width * 0.65)  # 312
        window_height = int(base_height * 0.65)  # 390
        
        # 角色预期尺寸（窗口的80%宽度，70%高度）
        char_width = int(window_width * 0.8)  # 250
        char_height = int(window_height * 0.7)  # 273
        
        print(f"📐 窗口尺寸: {window_width} x {window_height}")
        print(f"🎭 角色最大尺寸应该是: {char_width} x {char_height}")
        
        # 创建应用
        app = QApplication(sys.argv)
        pet_display = DesktopPetDisplay()
        
        # 获取初始设置的尺寸
        initial_max_size = pet_display.animation_label.maximumSize()
        print(f"🎯 初始设置的角色最大尺寸: {initial_max_size.width()} x {initial_max_size.height()}")
        
        # 显示窗口
        pet_display.show()
        
        # 等待动画加载后再检查
        app.processEvents()
        
        # 检查动画加载后的尺寸
        final_max_size = pet_display.animation_label.maximumSize()
        print(f"🎬 动画加载后的角色最大尺寸: {final_max_size.width()} x {final_max_size.height()}")
        
        # 验证是否保持了我们设置的限制
        if (final_max_size.width() == char_width and 
            final_max_size.height() == char_height):
            print("✅ 修复成功！角色大小限制已正确保持")
        else:
            print("❌ 仍有问题，角色大小被重新设置了")
            
        print("\n🔍 请检查:")
        print("- 角色是否显示为适中大小")
        print("- 角色是否不会被其他控件遮挡")
        print("- 角色是否保持正确比例")
        print("\n按 Ctrl+C 退出测试")
        
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_fixed_size()
