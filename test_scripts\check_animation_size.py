#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtGui import QMovie

def check_animation_size():
    """检查动画文件的原始尺寸"""
    try:
        print("🔍 检查动画文件尺寸...")
        
        # 获取动画文件路径
        base_path = Path(__file__).parent.parent
        main_gif_path = base_path / "assets" / "characters" / "default" / "main.gif"
        
        if not main_gif_path.exists():
            print(f"❌ 动画文件不存在: {main_gif_path}")
            return
            
        print(f"📁 动画文件路径: {main_gif_path}")
        
        # 创建QApplication（必需）
        app = QApplication(sys.argv)
        
        # 加载动画
        movie = QMovie(str(main_gif_path))
        if not movie.isValid():
            print("❌ 动画文件无效")
            return
            
        # 获取原始尺寸
        original_size = movie.scaledSize()
        print(f"🎬 动画原始尺寸: {original_size.width()} x {original_size.height()}")
        
        # 计算当前窗口和标签尺寸
        window_width, window_height = 312, 390  # 当前配置的窗口尺寸
        
        # 当前代码中的计算方式
        available_width_old = int(window_width * 0.8)  # 249
        available_height_old = int(window_height * 0.7)  # 273
        
        # 标签设置的最大尺寸
        label_max_width = int(window_width * 0.9)  # 280
        label_max_height = int(window_height * 0.75)  # 292
        
        print(f"\n📐 尺寸对比:")
        print(f"   动画原始尺寸: {original_size.width()} x {original_size.height()}")
        print(f"   代码计算尺寸: {available_width_old} x {available_height_old}")
        print(f"   标签最大尺寸: {label_max_width} x {label_max_height}")
        
        # 计算缩放后的尺寸
        from PyQt5.QtCore import Qt
        scaled_size_old = original_size.scaled(available_width_old, available_height_old, Qt.KeepAspectRatio)
        scaled_size_new = original_size.scaled(label_max_width, label_max_height, Qt.KeepAspectRatio)
        
        print(f"\n🎯 缩放结果:")
        print(f"   当前代码缩放: {scaled_size_old.width()} x {scaled_size_old.height()}")
        print(f"   应该的缩放: {scaled_size_new.width()} x {scaled_size_new.height()}")
        
        # 检查是否超出标签范围
        if (scaled_size_old.width() > label_max_width or 
            scaled_size_old.height() > label_max_height):
            print(f"\n❌ 问题发现:")
            print(f"   动画缩放尺寸 ({scaled_size_old.width()}x{scaled_size_old.height()}) 超出了标签最大尺寸 ({label_max_width}x{label_max_height})")
            print(f"   这会导致角色显示不全！")
        else:
            print(f"\n✅ 尺寸正常")
            
        print(f"\n🔧 修复建议:")
        print(f"   1. 统一动画缩放尺寸和标签最大尺寸")
        print(f"   2. 使用标签的最大尺寸来计算动画缩放")
        print(f"   3. 确保动画尺寸不超过标签容器")
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_animation_size()
