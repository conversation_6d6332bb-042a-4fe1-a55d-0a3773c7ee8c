#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.utils.config_manager import Config<PERSON>ana<PERSON>

def verify_larger_window():
    """验证更大窗口的设置"""
    try:
        print("🔍 验证更大窗口的设置...")
        
        config_manager = ConfigManager()
        config = config_manager.get_full_config()
        
        # 检查当前配置
        pet_config = config.get("DESKTOP_PET", {})
        enabled = pet_config.get("enabled", False)
        size_scale = pet_config.get("size_scale", 1.0)
        
        print(f"✅ 桌面宠物模式: {'已启用' if enabled else '未启用'}")
        print(f"✅ 窗口缩放比例: {size_scale}")
        
        # 计算实际尺寸
        base_width, base_height = 480, 600
        window_width = int(base_width * size_scale)
        window_height = int(base_height * size_scale)
        
        # 角色区域尺寸（70%宽度，60%高度）
        char_width = int(window_width * 0.7)
        char_height = int(window_height * 0.6)
        
        # 文字区域尺寸
        text_height = 25
        text_y = window_height - text_height - 5
        
        print(f"\n📐 新的布局信息:")
        print(f"   窗口尺寸: {window_width} x {window_height} 像素")
        print(f"   角色区域: {char_width} x {char_height} 像素")
        print(f"   文字区域: {window_width-10} x {text_height} 像素 (y={text_y})")
        
        print(f"\n📊 尺寸对比:")
        print(f"   修改前窗口: 312 x 390 像素")
        print(f"   修改后窗口: {window_width} x {window_height} 像素")
        print(f"   窗口增大: {((window_width * window_height) / (312 * 390) - 1) * 100:.1f}%")
        
        print(f"\n🎭 角色显示空间:")
        print(f"   修改前角色区域: 218 x 234 像素 (51,012 像素²)")
        print(f"   修改后角色区域: {char_width} x {char_height} 像素 ({char_width * char_height:,} 像素²)")
        print(f"   角色空间增大: {((char_width * char_height) / (218 * 234) - 1) * 100:.1f}%")
        
        print(f"\n🎯 预期效果:")
        print(f"   ✅ 窗口更大，角色有更多显示空间")
        print(f"   ✅ 角色不会被'框住'或裁剪")
        print(f"   ✅ 角色和文字都有充足的空间")
        print(f"   ✅ 整体显示更加舒适")
        
        if size_scale == 1.0:
            print(f"\n✨ 当前使用原始大小 (1.0倍)，应该有足够的显示空间！")
        elif size_scale < 1.0:
            print(f"\n⚠️  当前缩放比例较小 ({size_scale})，如果还觉得小可以增大到1.2或1.5")
        else:
            print(f"\n🎉 当前使用放大尺寸 ({size_scale}倍)，应该很宽敞！")
            
        if not enabled:
            print(f"\n⚠️  注意: 桌面宠物模式未启用")
            print(f"   运行以下命令启用: python enable_desktop_pet.py")
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    verify_larger_window()
