#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from src.display.desktop_pet_display import DesktopPetDisplay
from src.utils.config_manager import ConfigManager

def test_window_size():
    """测试新的窗口大小"""
    try:
        # 确保桌面宠物模式已启用
        config_manager = ConfigManager()
        config = config_manager.get_full_config()
        
        if "DESKTOP_PET" not in config:
            config["DESKTOP_PET"] = {}
        
        config["DESKTOP_PET"]["enabled"] = True
        config_manager.save_config(config)
        
        # 创建应用
        app = QApplication(sys.argv)
        
        # 创建桌面宠物显示
        pet_display = DesktopPetDisplay()
        
        # 显示窗口信息
        size = pet_display.size()
        print(f"窗口大小: {size.width()} x {size.height()} 像素")
        
        # 测试显示长文本
        pet_display.show_status_bubble("这是一段比较长的聊天文字，用来测试新的窗口大小是否能够更好地显示聊天内容。", 5000)
        
        # 显示窗口
        pet_display.show()
        
        print("桌面宠物已启动，窗口大小已更新！")
        print("请检查:")
        print("1. 窗口是否比之前更大")
        print("2. 聊天文字是否显示更清楚")
        print("3. 右键菜单中是否有'界面设置'->'调整窗口大小'选项")
        print("\n按 Ctrl+C 退出测试")
        
        # 运行应用
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_window_size()
