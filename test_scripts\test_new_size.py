#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PyQt5.QtWidgets import QApplication
from src.display.desktop_pet_display import DesktopPetDisplay
from src.utils.config_manager import ConfigMana<PERSON>

def test_new_size():
    """测试新的角色尺寸设置"""
    try:
        print("🔧 测试新的角色尺寸设置...")
        
        # 确保配置正确
        config_manager = ConfigManager()
        config = config_manager.get_full_config()
        
        if "DESKTOP_PET" not in config:
            config["DESKTOP_PET"] = {}
        
        config["DESKTOP_PET"]["enabled"] = True
        config["DESKTOP_PET"]["size_scale"] = 0.65
        
        config_manager.save_config(config)
        print("✅ 配置已设置")
        
        # 计算预期尺寸
        base_width, base_height = 480, 600
        window_width = int(base_width * 0.65)  # 312
        window_height = int(base_height * 0.65)  # 390
        
        # 新的角色预期尺寸（窗口的70%宽度，60%高度）
        char_width = int(window_width * 0.7)  # 218
        char_height = int(window_height * 0.6)  # 234
        
        print(f"📐 窗口尺寸: {window_width} x {window_height}")
        print(f"🎭 角色最大尺寸应该是: {char_width} x {char_height}")
        print(f"📝 文字区域位置: y={window_height - 25 - 5} (底部)")
        
        # 创建应用
        app = QApplication(sys.argv)
        pet_display = DesktopPetDisplay()
        
        # 获取实际设置的尺寸
        actual_max_size = pet_display.animation_label.maximumSize()
        actual_geometry = pet_display.animation_label.geometry()
        
        print(f"🎯 实际角色最大尺寸: {actual_max_size.width()} x {actual_max_size.height()}")
        print(f"📍 实际角色位置: x={actual_geometry.x()}, y={actual_geometry.y()}")
        print(f"📏 实际角色区域: {actual_geometry.width()} x {actual_geometry.height()}")
        
        # 检查文字区域
        text_geometry = pet_display.scroll_text_label.geometry()
        print(f"📝 文字区域位置: x={text_geometry.x()}, y={text_geometry.y()}")
        print(f"📝 文字区域尺寸: {text_geometry.width()} x {text_geometry.height()}")
        
        # 验证是否有足够空间
        char_bottom = actual_geometry.y() + actual_geometry.height()
        text_top = text_geometry.y()
        gap = text_top - char_bottom
        
        print(f"\n🔍 空间分析:")
        print(f"   角色底部位置: y={char_bottom}")
        print(f"   文字顶部位置: y={text_top}")
        print(f"   中间间隙: {gap}像素")
        
        if gap >= 5:
            print("✅ 角色和文字之间有足够间隙，不会重叠")
        else:
            print("⚠️  角色和文字可能重叠")
            
        # 显示窗口
        pet_display.show()
        
        print(f"\n🎯 修复要点:")
        print(f"   ✅ 角色区域缩小到70%×60%，给角色更多缩放空间")
        print(f"   ✅ 动画缩放使用标签的实际最大尺寸")
        print(f"   ✅ 角色位置上移，距离顶部10像素")
        print(f"   ✅ 文字固定在底部，不会遮挡角色")
        
        print("\n🔍 请检查:")
        print("- 角色是否完整显示，不被裁剪")
        print("- 角色大小是否适中")
        print("- 文字是否在底部正确显示")
        print("\n按 Ctrl+C 退出测试")
        
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_new_size()
