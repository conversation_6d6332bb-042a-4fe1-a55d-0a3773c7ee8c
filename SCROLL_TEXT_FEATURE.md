# 滚动文字显示功能

## 🎯 用户需求
1. 文字采用滚动的形式显示，不占用太大的气泡状态框
2. 解决整体UI界面被框框住、显示不全的感觉

## ✅ 实现的功能

### 1. 滚动文字显示
- **替代原来的大气泡框**：使用小巧的滚动文字条
- **智能显示模式**：
  - 短文本（≤20字符）：直接显示
  - 长文本（>20字符）：滚动显示
- **半透明背景**：黑色半透明，不遮挡角色

### 2. 优化的界面布局
- **减少预留空间**：从120像素减少到50像素
- **增大角色显示区域**：给角色更多空间
- **减少边距**：让角色更自然地显示

### 3. 滚动效果参数
```python
# 滚动参数
scroll_speed = 1        # 每次滚动1个字符
update_interval = 200   # 每200ms更新一次
visible_length = 20     # 可见文字长度
```

### 4. 显示时间计算
| 文本类型 | 显示时间 | 说明 |
|----------|----------|------|
| 短文本(≤20字符) | 5秒 | 直接显示 |
| 长文本(>20字符) | 5秒 + 文本长度×150ms | 滚动显示，最长20秒 |

## 🎨 样式设计

### 滚动文字条样式
```css
QLabel {
    background-color: rgba(0, 0, 0, 100);  /* 半透明黑色 */
    color: white;                          /* 白色文字 */
    font-size: 12px;                       /* 12像素字体 */
    font-weight: bold;                     /* 粗体 */
    padding: 5px 10px;                     /* 内边距 */
    border-radius: 10px;                   /* 圆角 */
    max-height: 30px;                      /* 固定高度 */
    min-height: 30px;
}
```

### 窗口布局优化
- **角色显示区域**：340×400像素（在360×450窗口内）
- **滚动文字预留**：50像素高度
- **边距设置**：5像素（减少空间浪费）

## 🔧 技术实现

### 核心方法
1. **`start_scroll_text(text, duration)`**：开始滚动显示
2. **`update_scroll_text()`**：更新滚动位置
3. **`stop_scroll_text()`**：停止滚动并隐藏

### 滚动逻辑
```python
def update_scroll_text(self):
    if text_len <= 20:
        # 短文本直接显示
        self.scroll_text_label.setText(self.current_scroll_text)
    else:
        # 长文本滚动显示
        visible_text = text[position:position + 20]
        if len(visible_text) < 20:
            # 循环显示：末尾 + " | " + 开头
            visible_text += " | " + text[:remaining_length]
```

## 🚀 使用效果

### 优势
1. **节省空间**：滚动文字条只占用30像素高度
2. **不遮挡角色**：半透明设计，角色清晰可见
3. **完整显示**：长文本通过滚动完整展示
4. **视觉友好**：小巧精致，不破坏整体美观

### 适用场景
- ✅ 聊天消息显示
- ✅ 状态提示信息
- ✅ TTS文本预览
- ✅ 系统通知

## 📱 测试方法

### 快速测试
```bash
python test_scroll_text.py
```

### 测试内容
1. **短文本测试**：直接显示效果
2. **中等文本测试**：滚动开始效果
3. **长文本测试**：完整滚动循环

### 观察要点
- 角色是否显示更完整
- 短文本是否直接显示
- 长文本是否平滑滚动
- 滚动文字是否不遮挡角色

## 🔄 与原功能的对比

| 特性 | 原气泡框 | 新滚动文字 |
|------|----------|------------|
| 占用空间 | 大（可变高度） | 小（固定30px） |
| 长文本处理 | 换行显示 | 滚动显示 |
| 角色遮挡 | 可能遮挡 | 不遮挡 |
| 视觉效果 | 突兀 | 自然 |
| 空间利用 | 低 | 高 |
