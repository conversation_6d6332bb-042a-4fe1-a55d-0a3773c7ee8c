#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.utils.config_manager import ConfigManager

def set_optimal_size():
    """设置最佳窗口尺寸"""
    try:
        config_manager = ConfigManager()
        config = config_manager.get_full_config()
        
        # 确保桌面宠物配置存在
        if "DESKTOP_PET" not in config:
            config["DESKTOP_PET"] = {}
        
        # 设置0.75倍缩放比例
        config["DESKTOP_PET"]["size_scale"] = 0.75
        config["DESKTOP_PET"]["enabled"] = True
        
        # 保存配置
        config_manager.save_config(config)
        
        # 计算新的窗口大小
        base_width, base_height = 480, 600
        scale = 0.75
        actual_width = int(base_width * scale)
        actual_height = int(base_height * scale)
        
        print("✅ 窗口大小已调整为最佳尺寸！")
        print(f"新的窗口大小: {actual_width} x {actual_height} 像素")
        print(f"缩放比例: {scale}")
        print("\n这个尺寸应该既不会太大，也有足够空间显示聊天文字。")
        print("字体大小也将调整为12px。")
        print("\n重新启动应用以查看效果：python main.py --mode gui")
        
    except Exception as e:
        print(f"❌ 设置失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    set_optimal_size()
