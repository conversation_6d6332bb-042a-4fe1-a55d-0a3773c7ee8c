# 桌面宠物窗口大小优化

## 🎯 问题描述
用户反馈聊天的语音文字看不全，界面太小了。

## ✅ 解决方案

### 1. 增加基础窗口尺寸
- **原始尺寸**: 320 x 450 像素
- **新尺寸**: 480 x 600 像素
- **改进**: 宽度增加50%，高度增加33%

### 2. 优化状态气泡显示
- 启用自动换行 (`setWordWrap(True)`)
- 增加字体大小：12px → 14px
- 增加内边距：8px 12px → 12px 16px
- 设置最大宽度：400px
- 提高背景透明度：200 → 220

### 3. 添加动态大小调整功能
- 在右键菜单中添加"界面设置" → "调整窗口大小"选项
- 支持0.5-3.0倍缩放比例
- 实时调整窗口大小
- 配置自动保存

### 4. 当前配置状态
- 缩放比例设置为1.2（比默认大20%）
- 实际窗口大小：576 x 720 像素
- 为聊天文字提供了充足的显示空间

## 🚀 使用方法

### 方法1：使用脚本快速设置
```bash
# 设置为更大尺寸（推荐）
python set_larger_size.py

# 检查当前配置
python check_config.py
```

### 方法2：通过右键菜单调整
1. 右键点击桌面宠物
2. 选择"基础设置" → "界面设置" → "调整窗口大小"
3. 输入缩放比例（建议1.0-1.5）
4. 确认应用

### 方法3：手动编辑配置文件
在 `config/config.json` 中设置：
```json
{
  "DESKTOP_PET": {
    "enabled": true,
    "size_scale": 1.2
  }
}
```

## 📊 效果对比

| 项目 | 原始 | 优化后 |
|------|------|--------|
| 基础尺寸 | 320x450 | 480x600 |
| 实际尺寸（1.2倍） | 384x540 | 576x720 |
| 字体大小 | 12px | 14px |
| 自动换行 | ❌ | ✅ |
| 动态调整 | ❌ | ✅ |

## 🎉 预期效果
- 聊天文字有更多显示空间
- 长文本自动换行，不会被截断
- 用户可以根据屏幕大小自定义窗口尺寸
- 更好的视觉体验和可读性

## 🔧 技术细节
- 修改文件：`src/display/desktop_pet_display.py`
- 主要改动：
  - `setup_ui()` 方法中的基础尺寸设置
  - 状态气泡样式优化
  - 新增 `set_window_size()` 方法
  - 右键菜单扩展
