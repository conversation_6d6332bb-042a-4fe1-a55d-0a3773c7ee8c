import os
import sys

# 获取项目根目录的绝对路径
if getattr(sys, 'frozen', False):
    # 打包环境
    if hasattr(sys, '_MEIPASS'):
        base_path = sys._MEIPASS
    else:
        base_path = os.path.dirname(sys.executable)
else:
    # 开发环境
    base_path = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

character_path = os.path.join(base_path, "assets", "characters", "default")
animation_states = ["main", "idle", "listening", "speaking", "thinking"]

print(f"项目根目录: {base_path}")
print(f"动画文件搜索路径: {character_path}")
print()

for state in animation_states:
    animation_file = os.path.join(character_path, f"{state}.gif")
    exists = os.path.exists(animation_file)
    print(f"{state}.gif: {animation_file} - {'存在' if exists else '不存在'}") 