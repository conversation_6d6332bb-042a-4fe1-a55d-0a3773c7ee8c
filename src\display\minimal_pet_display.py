#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
from PyQt5.QtWidgets import (QWidget, QLabel, QVBoxLayout, QApplication, 
                             QMenu, QAction)
from PyQt5.QtCore import Qt, QTimer, QSize
from PyQt5.QtGui import QMovie, QPixmap, QPainter, QBrush, QColor

class MinimalPetDisplay(QWidget):
    """极简桌面宠物显示 - 只显示角色，无边框无背景"""
    
    def __init__(self, config_manager=None):
        super().__init__()
        self.config_manager = config_manager
        self.current_animation = None
        self.current_state = "idle"
        
        # 获取缩放比例
        self.size_scale = 0.5  # 默认更小的比例
        if config_manager:
            try:
                config = config_manager.get_full_config()
                pet_config = config.get("DESKTOP_PET", {})
                self.size_scale = float(pet_config.get("size_scale", 0.5))
            except:
                pass
        
        self.setup_ui()
        self.load_animation("idle")
        
    def setup_ui(self):
        """设置极简UI"""
        # 窗口设置 - 完全透明，无边框
        self.setWindowFlags(
            Qt.FramelessWindowHint |  # 无边框
            Qt.WindowStaysOnTopHint |  # 置顶
            Qt.Tool |  # 不在任务栏显示
            Qt.SubWindow  # 子窗口
        )
        
        # 设置透明背景
        self.setAttribute(Qt.WA_TranslucentBackground)
        self.setAttribute(Qt.WA_NoSystemBackground)
        
        # 计算角色尺寸
        char_size = int(200 * self.size_scale)  # 基础200像素
        self.setFixedSize(char_size, char_size)
        
        # 布局 - 无边距
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # 角色标签
        self.character_label = QLabel()
        self.character_label.setAlignment(Qt.AlignCenter)
        self.character_label.setStyleSheet("background: transparent;")
        self.character_label.setFixedSize(char_size, char_size)
        
        layout.addWidget(self.character_label)
        
        # 右键菜单
        self.setContextMenuPolicy(Qt.CustomContextMenu)
        self.customContextMenuRequested.connect(self.show_context_menu)
        
        # 使窗口可拖拽
        self.dragging = False
        self.drag_position = None
        
    def load_animation(self, state):
        """加载动画"""
        try:
            # 动画文件路径
            animations_dir = os.path.join(os.path.dirname(__file__), "..", "assets", "animations")
            animation_path = os.path.join(animations_dir, f"{state}.gif")
            
            if not os.path.exists(animation_path):
                # 如果没有对应动画，使用默认图片
                default_img = os.path.join(animations_dir, "default.png")
                if os.path.exists(default_img):
                    pixmap = QPixmap(default_img)
                    char_size = int(200 * self.size_scale)
                    scaled_pixmap = pixmap.scaled(char_size, char_size, 
                                                Qt.KeepAspectRatio, 
                                                Qt.SmoothTransformation)
                    self.character_label.setPixmap(scaled_pixmap)
                return
            
            # 停止当前动画
            if self.current_animation:
                self.current_animation.stop()
            
            # 加载新动画
            self.current_animation = QMovie(animation_path)
            
            # 设置动画尺寸
            char_size = int(200 * self.size_scale)
            self.current_animation.setScaledSize(QSize(char_size, char_size))
            
            # 设置到标签
            self.character_label.setMovie(self.current_animation)
            self.current_animation.start()
            
            self.current_state = state
            
        except Exception as e:
            print(f"加载动画失败: {e}")
    
    def mousePressEvent(self, event):
        """鼠标按下事件 - 开始拖拽"""
        if event.button() == Qt.LeftButton:
            self.dragging = True
            self.drag_position = event.globalPos() - self.frameGeometry().topLeft()
            event.accept()
    
    def mouseMoveEvent(self, event):
        """鼠标移动事件 - 拖拽窗口"""
        if event.buttons() == Qt.LeftButton and self.dragging:
            self.move(event.globalPos() - self.drag_position)
            event.accept()
    
    def mouseReleaseEvent(self, event):
        """鼠标释放事件 - 结束拖拽"""
        if event.button() == Qt.LeftButton:
            self.dragging = False
            event.accept()
    
    def show_context_menu(self, position):
        """显示右键菜单"""
        menu = QMenu(self)
        
        # 动画选项
        idle_action = QAction("待机", self)
        idle_action.triggered.connect(lambda: self.load_animation("idle"))
        menu.addAction(idle_action)
        
        happy_action = QAction("开心", self)
        happy_action.triggered.connect(lambda: self.load_animation("happy"))
        menu.addAction(happy_action)
        
        menu.addSeparator()
        
        # 大小选项
        size_menu = menu.addMenu("大小")
        
        for scale, name in [(0.3, "很小"), (0.5, "小"), (0.7, "中"), (1.0, "大")]:
            action = QAction(name, self)
            action.triggered.connect(lambda checked, s=scale: self.change_size(s))
            size_menu.addAction(action)
        
        menu.addSeparator()
        
        # 退出
        exit_action = QAction("退出", self)
        exit_action.triggered.connect(self.close)
        menu.addAction(exit_action)
        
        menu.exec_(self.mapToGlobal(position))
    
    def change_size(self, new_scale):
        """改变大小"""
        self.size_scale = new_scale
        
        # 重新计算尺寸
        char_size = int(200 * self.size_scale)
        self.setFixedSize(char_size, char_size)
        self.character_label.setFixedSize(char_size, char_size)
        
        # 重新加载动画以应用新尺寸
        if self.current_animation:
            self.current_animation.setScaledSize(QSize(char_size, char_size))
        
        # 保存配置
        if self.config_manager:
            try:
                config = self.config_manager.get_full_config()
                if "DESKTOP_PET" not in config:
                    config["DESKTOP_PET"] = {}
                config["DESKTOP_PET"]["size_scale"] = new_scale
                self.config_manager.save_config(config)
            except:
                pass

def main():
    """测试运行"""
    app = QApplication(sys.argv)
    
    # 创建极简宠物
    pet = MinimalPetDisplay()
    pet.show()
    
    # 设置初始位置
    pet.move(100, 100)
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
