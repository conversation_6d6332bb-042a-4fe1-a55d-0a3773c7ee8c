#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.utils.config_manager import Config<PERSON>ana<PERSON>

def verify_fix():
    """验证修复效果"""
    try:
        print("🔍 验证角色显示修复效果...")
        
        config_manager = ConfigManager()
        config = config_manager.get_full_config()
        
        # 检查当前配置
        pet_config = config.get("DESKTOP_PET", {})
        enabled = pet_config.get("enabled", False)
        size_scale = pet_config.get("size_scale", 1.0)
        
        print(f"✅ 桌面宠物模式: {'已启用' if enabled else '未启用'}")
        print(f"✅ 窗口缩放比例: {size_scale}")
        
        # 计算实际尺寸
        base_width, base_height = 480, 600
        window_width = int(base_width * size_scale)
        window_height = int(base_height * size_scale)
        
        # 角色区域尺寸（新的布局方式）
        char_width = int(window_width * 0.9)  # 90%宽度
        char_height = int(window_height * 0.75)  # 75%高度
        
        # 文字区域尺寸
        text_height = 25
        text_y = window_height - text_height - 5
        
        print(f"\n📐 布局信息:")
        print(f"   窗口尺寸: {window_width} x {window_height}")
        print(f"   角色区域: {char_width} x {char_height} (位置: 居中上部)")
        print(f"   文字区域: {window_width-10} x {text_height} (位置: 底部 y={text_y})")
        
        print(f"\n🎯 修复要点:")
        print(f"   ✅ 使用绝对定位，避免布局冲突")
        print(f"   ✅ 角色占据窗口上部75%区域")
        print(f"   ✅ 文字固定在底部，不会遮挡角色")
        print(f"   ✅ 角色大小不会被动画加载覆盖")
        
        print(f"\n🚀 现在角色应该:")
        print(f"   - 显示在窗口上部，大小适中")
        print(f"   - 不会被底部文字遮挡")
        print(f"   - 保持正确的宽高比")
        print(f"   - 大小稳定，不会变化")
        
        if not enabled:
            print(f"\n⚠️  注意: 桌面宠物模式未启用")
            print(f"   运行以下命令启用: python enable_desktop_pet.py")
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    verify_fix()
