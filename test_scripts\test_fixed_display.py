#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from src.display.desktop_pet_display import DesktopPetDisplay
from src.utils.config_manager import ConfigManager

def test_fixed_display():
    """测试修复后的显示效果"""
    try:
        # 确保桌面宠物模式已启用，缩放比例为0.75
        config_manager = ConfigManager()
        config = config_manager.get_full_config()
        
        if "DESKTOP_PET" not in config:
            config["DESKTOP_PET"] = {}
        
        config["DESKTOP_PET"]["enabled"] = True
        config["DESKTOP_PET"]["size_scale"] = 0.75
        config_manager.save_config(config)
        
        # 创建应用
        app = QApplication(sys.argv)
        
        # 创建桌面宠物显示
        pet_display = DesktopPetDisplay()
        
        # 显示窗口信息
        size = pet_display.size()
        print(f"窗口大小: {size.width()} x {size.height()} 像素")
        print("修复内容:")
        print("1. ✅ 移除了setScaledContents(True)，防止角色变形")
        print("2. ✅ 增加了布局边距和间距，为气泡留出空间")
        print("3. ✅ 根据文本长度自动调整气泡显示时间")
        print("4. ✅ 优化了气泡样式，防止被遮挡")
        
        # 显示窗口
        pet_display.show()
        
        # 测试不同长度的文本
        def test_short_text():
            pet_display.show_status_bubble("短文本测试")
            
        def test_long_text():
            pet_display.show_status_bubble("这是一段比较长的聊天文字，用来测试气泡是否会被遮挡，以及文本是否能够完整显示。现在应该不会出现变形或被截断的问题了。")
            
        def test_very_long_text():
            pet_display.show_status_bubble("这是一段非常长的文本，用来测试极限情况下的显示效果。我们希望即使是很长的文本，也能够正确换行显示，不会被角色动画遮挡，并且有足够的显示时间让用户阅读完整的内容。这个测试将验证我们的修复是否有效。")
        
        # 定时测试不同文本
        QTimer.singleShot(1000, test_short_text)
        QTimer.singleShot(4000, test_long_text)
        QTimer.singleShot(10000, test_very_long_text)
        
        print("\n测试序列:")
        print("1秒后: 短文本测试")
        print("4秒后: 长文本测试")
        print("10秒后: 超长文本测试")
        print("\n请观察:")
        print("- 角色是否保持正确比例（不变形）")
        print("- 气泡文字是否完整显示（不被遮挡）")
        print("- 长文本是否有足够的显示时间")
        print("\n按 Ctrl+C 退出测试")
        
        # 运行应用
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_fixed_display()
