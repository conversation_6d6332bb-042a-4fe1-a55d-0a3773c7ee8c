#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from src.display.desktop_pet_display import DesktopPetDisplay
from src.utils.config_manager import ConfigMana<PERSON>

def test_scroll_text():
    """测试滚动文字效果"""
    try:
        # 确保桌面宠物模式已启用，缩放比例为0.75
        config_manager = ConfigManager()
        config = config_manager.get_full_config()
        
        if "DESKTOP_PET" not in config:
            config["DESKTOP_PET"] = {}
        
        config["DESKTOP_PET"]["enabled"] = True
        config["DESKTOP_PET"]["size_scale"] = 0.75
        config_manager.save_config(config)
        
        # 创建应用
        app = QApplication(sys.argv)
        
        # 创建桌面宠物显示
        pet_display = DesktopPetDisplay()
        
        # 显示窗口信息
        size = pet_display.size()
        print(f"窗口大小: {size.width()} x {size.height()} 像素")
        
        print("\n新功能:")
        print("1. ✅ 滚动文字显示，替代大气泡框")
        print("2. ✅ 角色显示区域增大，减少被框住的感觉")
        print("3. ✅ 长文本自动滚动，短文本直接显示")
        print("4. ✅ 半透明黑色背景，不遮挡角色")
        
        # 显示窗口
        pet_display.show()
        
        # 测试不同长度的文本
        def test_short_text():
            pet_display.show_status_bubble("短文本测试")
            print("显示短文本: 短文本测试")
            
        def test_medium_text():
            pet_display.show_status_bubble("这是一段中等长度的文字，用来测试滚动效果。")
            print("显示中等文本: 这是一段中等长度的文字，用来测试滚动效果。")
            
        def test_long_text():
            pet_display.show_status_bubble("这是一段很长的聊天文字，用来测试滚动显示功能。现在文字会在一个小的半透明框中滚动显示，不会占用太大的空间，也不会遮挡角色的显示。这样既能看到完整的文字内容，又能保持界面的简洁美观。")
            print("显示长文本: 滚动显示测试")
        
        # 定时测试不同文本
        QTimer.singleShot(1000, test_short_text)
        QTimer.singleShot(7000, test_medium_text)
        QTimer.singleShot(15000, test_long_text)
        
        print("\n测试序列:")
        print("1秒后: 短文本测试（直接显示）")
        print("7秒后: 中等文本测试（可能滚动）")
        print("15秒后: 长文本测试（滚动显示）")
        print("\n请观察:")
        print("- 角色是否显示更完整（不被框住）")
        print("- 短文本是否直接显示")
        print("- 长文本是否滚动显示")
        print("- 滚动文字是否不遮挡角色")
        print("\n按 Ctrl+C 退出测试")
        
        # 运行应用
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_scroll_text()
